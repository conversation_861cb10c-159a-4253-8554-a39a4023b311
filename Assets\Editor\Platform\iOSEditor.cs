﻿#if UNITY_EDITOR && UNITY_IOS
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.iOS.Xcode;
using static SDKConfig;
using static UnityEngine.GraphicsBuffer;

public class iOSEditor : IEditor
{

    public bool OnPreprocessBuild(SDKConfig config)
    {

        return true;
    }

    public void OnPostprocessBuild(SDKConfig config)
    {
        // 修改 Xcode 工程
        ModifyXcodeProject(config);
        // 修改 Info.plist 文件
        ModifyInfoPlist(config);
    }


    private static void ModifyXcodeProject(SDKConfig config)
    {
        // Xcode 工程路径
        string pathToBuiltProject = config.buildReport.summary.outputPath;
        string projectPath = PBXProject.GetPBXProjectPath(pathToBuiltProject);

        // 加载 Xcode 工程
        PBXProject project = new PBXProject();
        project.ReadFromFile(projectPath);

        // 获取 Unity 的主 target 和 framework target
#if UNITY_2019_3_OR_NEWER
        string mainTarget = project.GetUnityMainTargetGuid();
        string frameworkTarget = project.GetUnityFrameworkTargetGuid();
        // 如果需要，也为 Framework target 添加 -ObjC
        project.AddBuildProperty(frameworkTarget, "OTHER_LDFLAGS", "-ObjC");
#else
       string mainTarget = project.TargetGuidByName(PBXProject.GetUnityTargetName());
#endif

        // 修改最低支持版本为 iOS 12.0
        project.SetBuildProperty(mainTarget, "IPHONEOS_DEPLOYMENT_TARGET", "12.0");

        // 禁用 Bitcode
        project.SetBuildProperty(mainTarget, "ENABLE_BITCODE", "NO");

        // 添加 Other Linker Flags: -ObjC
        project.AddBuildProperty(mainTarget, "OTHER_LDFLAGS", "-ObjC");

        // 为主目标 Unity-iPhone 添加 Runpath Search Paths: /usr/lib/swift
        project.AddBuildProperty(mainTarget, "LD_RUNPATH_SEARCH_PATHS", "/usr/lib/swift");

#if UNITY_2019_3_OR_NEWER
        // 为 UnityFramework 目标添加 Runpath Search Paths: /usr/lib/swift
        project.AddBuildProperty(frameworkTarget, "LD_RUNPATH_SEARCH_PATHS", "/usr/lib/swift");
#endif

        // 添加系统 Framework（默认）
        project.AddFrameworkToProject(mainTarget, "CoreTelephony.framework", false);
        project.AddFrameworkToProject(mainTarget, "StoreKit.framework", false);
        project.AddFrameworkToProject(mainTarget, "UserNotifications.framework", false);
        project.AddFrameworkToProject(mainTarget, "UnityFramework.framework", false);

        // 遍历系统库列表并添加
        List<string> libs = config.libs;
        if (libs != null && libs.Count > 0)
        {
            foreach (string lib in libs)
            {
                // 确保框架名称以 .framework 结尾
                string frameworkName = lib.EndsWith(".framework") ? lib : lib + ".framework";
                // 默认添加到unity-iphone主target
                project.AddFrameworkToProject(mainTarget, frameworkName, false);
            }
        }

        // 添加推送通知、内购、Sign in with Apple 等 Capability
        AddCapabilities(pathToBuiltProject, mainTarget);

        // 保存 Xcode 工程
        project.WriteToFile(projectPath);
    }

    private static void AddCapabilities(string pathToBuiltProject, string mainTarget)
    {
        // 创建 Entitlements 文件路径
        string entitlementsPath = Path.Combine(pathToBuiltProject, "Unity-iPhone.entitlements");

        // 创建或读取 Entitlements 文件
        PlistDocument entitlements = new PlistDocument();
        if (File.Exists(entitlementsPath))
        {
            entitlements.ReadFromFile(entitlementsPath);
        }

        // 获取根字典
        PlistElementDict rootDict = entitlements.root;

        // 添加推送通知权限
        rootDict.SetString("aps-environment", "production");

        // 添加 Sign in with Apple 权限
        rootDict.SetString("com.apple.developer.applesignin", "Default");

        // 添加内购权限
        PlistElementArray inAppPayments = rootDict.CreateArray("com.apple.developer.in-app-payments");
        inAppPayments.AddString("Merchant ID");

        // 保存 Entitlements 文件
        entitlements.WriteToFile(entitlementsPath);
    }

    private static void ModifyInfoPlist(SDKConfig config)
    {
        // Info.plist 文件路径
        string pathToBuiltProject = config.buildReport.summary.outputPath;
        string plistPath = Path.Combine(pathToBuiltProject, "Info.plist");

        // 读取 Info.plist 文件
        PlistDocument plist = new PlistDocument();
        plist.ReadFromFile(plistPath);

        // 获取根字典
        PlistElementDict rootDict = plist.root;

        List<KeyValuePairData> permissions = config.permissionMaps;
        if (permissions != null && permissions.Count > 0)
        {
            foreach (KeyValuePairData dict in permissions)
            {
                // 假设每个字典有key和value字段
                if (dict.name.Length > 0 && dict.value.Length > 0)
                {
                    rootDict.SetString(dict.name, dict.value);
                }
            }
        }

        List<KeyValuePairData> metas = config.metas;
        // 获取到配置的key列表
        List<string> metaNames = metas?.Select(m => m.name).ToList() ?? new List<string>();

        // 添加 Queried URL Schemes
        PlistElementArray queriedSchemes = rootDict.CreateArray("LSApplicationQueriesSchemes");
        PlistElementArray urlSchemesArray = rootDict.CreateArray("CFBundleURLTypes");

        if (config.area == 0)
        { //国内
            // 卓信id 默认添加
            rootDict.SetString("ZX_CHANNEL_ID", "C01-hsD63B6RhEbv");
            // 添加权限说明（国内默认需要相机、相册、idfa权限）
            rootDict.SetString("NSCameraUsageDescription", "应用需要访问相机来拍摄照片");
            rootDict.SetString("NSPhotoLibraryUsageDescription", "应用需要访问照片库来上传图片");
            rootDict.SetString("NSUserTrackingUsageDescription", "我们需要使用您的设备标识符来提供个性化广告服务");

            if (metaNames.Contains("Tencent_APPID")) // QQ登录分享相关白名单
            {
                queriedSchemes.AddString("tim");
                queriedSchemes.AddString("mqq");
                queriedSchemes.AddString("mqqapi");
                queriedSchemes.AddString("mqqbrowser");
                queriedSchemes.AddString("mttbrowser");
                queriedSchemes.AddString("mqqOpensdkSSoLogin");
                queriedSchemes.AddString("mqqopensdkapiV2");
                queriedSchemes.AddString("mqqopensdkapiV4");
                queriedSchemes.AddString("mqzone");
                queriedSchemes.AddString("mqzoneopensdk");
                queriedSchemes.AddString("mqzoneopensdkapi");
                queriedSchemes.AddString("mqzoneopensdkapi19");
                queriedSchemes.AddString("mqzoneopensdkapiV2");
                queriedSchemes.AddString("mqqapiwallet");
                queriedSchemes.AddString("mqqopensdkfriend");
                queriedSchemes.AddString("mqqopensdkavatar");
                queriedSchemes.AddString("mqqopensdkminiapp");
                queriedSchemes.AddString("mqqopensdkdateline");
                queriedSchemes.AddString("mqqgamebindinggroup");
                queriedSchemes.AddString("mqqopensdkgrouptribeshare");
                queriedSchemes.AddString("tencentapi.qq.reqContent");
                queriedSchemes.AddString("tencentapi.qzone.reqContent");
                queriedSchemes.AddString("mqqthirdappgroup");
                queriedSchemes.AddString("mqqopensdklaunchminiapp");
            }
            if (metaNames.Contains("Wechat_APPID")) // 微信登录分享相关白名单
            {
                queriedSchemes.AddString("weixin");
                queriedSchemes.AddString("weixinULAPI");
            }
            if (metaNames.Contains("TAPTAP_ClientID")) // TAPTAP登录等功能相关白名单
            {
                queriedSchemes.AddString("tapiosdk");
                queriedSchemes.AddString("tapsdk");
            }
            //地图相关 暂不需要
            // queriedSchemes.AddString("baidumap");
            // queriedSchemes.AddString("qqmap");
            // queriedSchemes.AddString("iosamap");            

        }
        else if (config.area == 1)
        {  //海外
            if (metaNames.Contains("FacebookAppID")) // Facebook登录分享相关白名单
            {
                queriedSchemes.AddString("fbapi");
                queriedSchemes.AddString("fb-messenger-share-api");
                queriedSchemes.AddString("fbauth2");
                queriedSchemes.AddString("fbshareextension");
                queriedSchemes.AddString("fbauth");
                queriedSchemes.AddString("fb");
            }
            if (metaNames.Contains("TwitterV2ClientIDKey")) // twitter登录相关白名单
            {
                queriedSchemes.AddString("twitter");
                queriedSchemes.AddString("twitterauth");

                //twitterv2
                PlistElementDict tw2UrlSchemeDict = urlSchemesArray.AddDict();
                tw2UrlSchemeDict.SetString("CFBundleURLName", "twitterV2");
                PlistElementArray tw2UrlSchemeItems = tw2UrlSchemeDict.CreateArray("CFBundleURLSchemes");
                tw2UrlSchemeItems.AddString("bctw");
            }
           
        }

        // 添加urlSchemes
        List<KeyValuePairData> urlSchemes = config.schemes;
        if (urlSchemes != null && urlSchemes.Count > 0)
        {
            foreach (KeyValuePairData dict in metas)
            {
                if (dict.name.Length > 0 && dict.value.Length > 0)
                {
                    PlistElementDict tw1UrlSchemeDict = urlSchemesArray.AddDict();
                    tw1UrlSchemeDict.SetString("CFBundleURLName", dict.name);
                    PlistElementArray tw1UrlSchemeItems = tw1UrlSchemeDict.CreateArray("CFBundleURLSchemes");
                    tw1UrlSchemeItems.AddString(dict.value);
                }
            }
        }


        Configuration sdkConfig = config.configurations;

        // 添加Q1SDKConfigure SDK项目数据
        PlistElementDict q1ConfigDic = rootDict.CreateDict("Q1SDKConfigure");
        q1ConfigDic.SetString("Q1_APPID", sdkConfig.appid);
        q1ConfigDic.SetString("Q1_APPKEY", sdkConfig.appkey);
        q1ConfigDic.SetString("Q1_DOMAIN", sdkConfig.domain);
        q1ConfigDic.SetString("Q1_PID", sdkConfig.pid);

        string environment = "PRO";
        //SDK环境  0：正式  4：debug  1：review
        if (config.enviroment == 0)
        {
            if (config.area == 1)
            {
                environment = "EA";
            }
            else
            {
                environment = "PRO";
            }
        }
        else if (config.enviroment == 4)
        {
            environment = "DEBUG";
        }
        else if (config.enviroment == 1)
        {
            environment = "REVIEW";
        }
        q1ConfigDic.SetString("Q1_ENVIRONMENT", environment);


        IReadOnlyList<string> sdkConfigureList = new List<string>() { "Q1_PRIVACYPOLICY", "Q1_ENABLELOG", "Q1_FB_FRIEND", "Q1_STOREKIT2_FIREBASE", "Q1_OPENPUSH", "Q1_VISITORUPGRADE", "Q1_BGSCRUB", "Q1_OPENPUSH", "Q1_FB_FRIEND", "Q1_PRIVACYPOLICY", "Q1_USERPROTOCOL", "Q1_THINKING_APPID","Q1_SHOW_TOAST"};
        IReadOnlyList<string> thirdConfigureList = new List<string>() { "Apple_TeamID", "TAPTAP_ClientID", "TAPTAP_ClientToken", "TAPTAP_ServerURL", "Tencent_APPID", "Tencent_Secret", "Tencent_UniversalLink", "Wechat_APPID", "Wechat_Secret", "Wechat_UniversalLink" };
        IReadOnlyList<string> adjustConfigureList = new List<string>() { "ADJUST_APPTOKEN", "ADJUST_SANDBOX_MODE", "ENABLE_LOG" };
        IReadOnlyList<string> tiktokConfigureList = new List<string>() { "TIKTOK_ID", "TIKTOK_APPID", "TIKTOK_DEBUG" };
        IReadOnlyList<string> aihelpConfigureList = new List<string>() { "AIHELP_APPKEY", "AIHELP_DOMAIN", "AIHELP_APPID","AIHELP_LANGUAGE" };

        // 先预检查是否存在各配置
        bool hasThirdConfig = false;
        bool hasAdjustConfig = false;
        bool hasTiktokConfig = false;
        bool hasAihelpConfig = false;

        if (metas != null && metas.Count > 0)
        {
            foreach (KeyValuePairData dict in metas)
            {
                if (dict.name.Length > 0 && dict.value.Length > 0)
                {
                    if (thirdConfigureList.Contains(dict.name))
                    {
                        hasThirdConfig = true;
                    }
                    else if (adjustConfigureList.Contains(dict.name))
                    {
                        hasAdjustConfig = true;
                    }
                    else if (tiktokConfigureList.Contains(dict.name))
                    {
                        hasTiktokConfig = true;
                    }
                    else if (aihelpConfigureList.Contains(dict.name))
                    {
                        hasAihelpConfig = true;
                    }
                }
            }
        }

        // 根据检查结果创建字典
        PlistElementDict thirdConfigDic = null;
        PlistElementDict adjustConfigDic = null;
        PlistElementDict tiktokConfigDic = null;
        PlistElementDict aihelpConfigDic = null;

        if (hasThirdConfig)
        {
            thirdConfigDic = rootDict.CreateDict("Q1ThirdLoginConfigure");
        }

        if (hasAdjustConfig)
        {
            adjustConfigDic = rootDict.CreateDict("AdjustConfigure");
        }

        if (hasTiktokConfig)
        {
            tiktokConfigDic = rootDict.CreateDict("TiktokConfigure");
        }

        if (hasAihelpConfig)
        {
            aihelpConfigDic = rootDict.CreateDict("AihelpConfigure");
        }


        if (metas != null && metas.Count > 0)
        {
            foreach (KeyValuePairData dict in metas)
            {
                if (dict.name.Length > 0 && dict.value.Length > 0)
                {
                    if (sdkConfigureList.Contains(dict.name))
                    {
                        // 添加到Q1SDK配置字典
                        SetDictionaryValue(q1ConfigDic, dict.name, dict.value);
                    }
                    else if (thirdConfigureList.Contains(dict.name) && thirdConfigDic != null)
                    {
                        // 添加到第三方配置字典
                        SetDictionaryValue(thirdConfigDic, dict.name, dict.value);
                    }
                    else if (adjustConfigureList.Contains(dict.name) && adjustConfigDic != null)
                    {
                        // 添加到adjust配置字典
                        SetDictionaryValue(adjustConfigDic, dict.name, dict.value);
                    }
                    else if (tiktokConfigureList.Contains(dict.name) && tiktokConfigDic != null)
                    {
                        // 添加到tiktok配置字典
                        SetDictionaryValue(tiktokConfigDic, dict.name, dict.value);
                    }
                    else if (aihelpConfigureList.Contains(dict.name) && aihelpConfigDic != null)
                    {
                        // 添加到aihelp配置字典
                        SetDictionaryValue(aihelpConfigDic, dict.name, dict.value);
                    }
                    else
                    {
                        // 添加到外层
                        SetDictionaryValue(rootDict, dict.name, dict.value);
                    }
                }
            }
        }

        // 保存 Info.plist 文件
        plist.WriteToFile(plistPath);
    }

    private static void SetDictionaryValue(PlistElementDict dictionary, string name, string value)
    {
        if (value.ToLower() == "true" || value.ToLower() == "false")
        {
            bool boolValue = value.ToLower() == "true";
            dictionary.SetBoolean(name, boolValue);
        }
        else
        {
            dictionary.SetString(name, value);
        }
    }
}



#endif