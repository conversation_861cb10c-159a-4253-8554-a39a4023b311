---

Thinking<PERSON>ey =
{
	merge_three = "merge_three",
	merge_five=  "merge_five",
	guide = "guide",
	resources_get = "resources_get",
	resources_use = "resources_use",
	cook_start = "cook_start",
	cook_complete = "cook_complete",
	exit_game = "exit_game",
	free_pass = "free_pass",
	iap_pass = "iap_pass",
	iap_pass3 = "iap_pass3",
	daily_tasks = "daily_tasks",
	weekly_tasks = "weekly_tasks",
	marge_task = "marge_task",
	npc_plan = "npc_plan",
	goods_plan = "goods_plan",
	flg = "flg",
	activity = "activity",
	open_inbox = "open_inbox",
	skin_plan = "skin_plan",
	open_faq = "open_faq",
	open_fb = "open_fb",
	mini_game = "mini_game",
	player_login_success = "player_login_success",
	player_login_updatesuccess = "player_login_updatesuccess",
	player_login_fail = "player_login_fail",
	player_login_updatefail = "player_login_updatefail",
	mission_success = "mission_success",
	get_mission_rewards = "get_mission_rewards",
	resource_change = "resource_change",
	
	enter_Easter = "enter_Easter",
	Easter_game_fail = "Easter_game_fail",
	exit_Easter = "exit_Easter",
	brianblessing= "brianblessing",
	funnymerging = "funnymerging",	
	start_Easter = "start_Easter",
	season_area = "sseason_area",
	flowerConters = "flowerConters",
	Home = "Home",
	open_gam = "open_gam",
	team = "team",
	pass_complete = "pass_complete",
	addInfinityTime = "addInfinityTime",
	use_magnet = "use_magnet",
	delete_account = "delete_account",
	team_activities = "team_activities",
	smartworker = "smartworker",
	HalloweenActivity = "HalloweenActivity",
	MonthlySeasonTask = "goldPass", --月度赛季完成任务
	ObjItemPayBox = "ObjItemPayBox",
	network_delay = "network_delay",
	frostyracing = "frostyracing",
	Dialog = "Dialog",
	click_contact = "click_contact",
	Goals_point = "Goals_point",
	Dice_get = "Dice_get",
	Dice_ues = "Dice_ues",
	Dice_monopoly = "Dice_monopoly",
	Dice_plant = "Dice_plant",
	Dice_ranking = "Dice_ranking",
	Dice_pass = "Dice_pass",
	Dice_mine = "Dice_mine",
	Dice_buy = "Dice_buy",
	redDiamond = "redDiamond",
	bingoPay = "bingoPay",
	b_DeBug_Log = "b_DeBug_Log",
	doubleMine = "doubleMine",
	doubleMineBox = "doubleMineBox",
	Latest_stage = "Latest_stage",
	limitQuests_complete = "limitQuests_complete",
	limitQuests_receive = "limitQuests_receive",
	remainsLv = "remainsLv",
	remainsRank = "remainsRank",
	remainsMerge = "remainsMerge",
	remainsPass = "remainsPass",
	skin_get = "skin_get",
    battle_activity = "battle_activity",
    battle_pass = "battle_pass",
    battle_rank = "battle_rank",
    battle_goal = "battle_goal",
	mining = "mining",
    WeekBattle = "WeekBattle",
	reddiamond_link = "reddiamond_link",
	reddiamondShop_link = "reddiamondShop_link",
	reddiamond_firstcharge = "reddiamond_firstcharge",
	mining_drop = "mining_drop",
    goldpass_get = "goldpass_get",
	CatFoodbox = "CatFoodbox",
	CatStar = "CatStar",
	CatLevel = "CatLevel",
	CatPass = "CatPass",
	CatRank = "CatRank",
    frostyracing_pass = "frostyracing_pass",
	BoxTap = "BoxTap",
    CardChange = "CardChange",
    CardBag = "CardBag",
	mining_finish = "mining_finish",
	GroundPollution = "GroundPollution",
    battle_tips = "battle_tips",  -- 勇士对决报名提示
    Team_Tech = "Team_Tech", -- 联盟科技捐献
    PointShop = "PointShop", -- 积分商店
	ChatSkin_Get = "ChatSkin_Get",--头像/头像框/气泡
    EquipDevelopment = "EquipDevelopment", -- 装备养成
	Mainlevel = "Mainlevel",--主线关卡
	ToyFactoryCard = "ToyFactoryCard",
	HeroDevelopment = "HeroDevelopment", -- 角色养成
	ZooWareHouse = "ZooWareHouse", -- 动物园仓库物品增加时上报
	TowerClimb = "TowerClimb", 	-- 爬塔结算上报
	login_slg_success = "login_success_slg",
	login_slg_start = "login_success_slg_start",
	login_slg_entry = "login_slg_entry",
	login_slg_restart = "login_slg_restart",
	login_restart = "login_restart",
	Toyreset = "Toyreset", -- 英雄重置上报
	Allianceboss = "Allianceboss", -- 挑战联盟boss上报
	Arena = "Arena",--竞技场
    Tradetruck = "Tradetruck",  -- 贸易货车
    CardUniversal = "CardUniversal",  -- 万能卡
	WorldBoss = "WorldBoss",  -- 世界BOSS挑战
	WorldBoss_reward = "WorldBoss_reward",  -- 世界BOSS成就领奖
	slg_cum_activities = "slg_cum_activities",--累充礼包
	slg_top_stake = "slg_top_stake",
	slg_top_stake_reward = "slg_top_stake_reward",
	slg_top_offering_flowers = "slg_top_offering_flowers",
	slg_top_smash_eggs = "slg_top_smash_eggs",
	slg_top_inspire = "slg_top_inspire",
}

UserSet = {
	latest_key_amount = "latest_key_amount",
	latest_dima_amount = "latest_dima_amount",
	latest_coin_amount = "latest_coin_amount",
	latest_flg_count = "latest_flg_count",
	latest_npc_count = "latest_npc_count",
	latest_marge_count = "latest_marge_count",
	total_login_days = "total_login_days",
	total_login_count = "total_login_count",
	first_payTime = "first_payTime",
	total_pay_count = "total_pay_count",
	total_pay_amount = "total_pay_amount",
	
	rank_Merge = "rank_Merge",
	rank_Island = "rank_Island",
	latest_login_time = "latest_login_time", 
	latest_level = "latest_level",
	
	first_pay_time = "first_pay_time",
	 
	is_pay_user = "is_pay_user",
	first_pay_level = "first_pay_level",
	first_pay_coin = "first_pay_coin",
	first_pay_usd = "first_pay_usd",
	first_pay_id = "first_pay_id",
	latest_pay_time = "latest_pay_time",
	latest_pay_level = "latest_pay_level",
	latest_pay_payid = "latest_pay_payid",
	role_id = "role_id",
	reg_time = "reg_time",
	
	
	task_daily_active = "task_daily_active",
	task_weekly_active = "task_weekly_active",
	weekly_card_expire_time = "weekly_card_expire_time",
	is_weekly_card_effective = "is_weekly_card_effective",
	monthly_card_expire_time = "monthly_card_expire_time",
	is_monthly_card_effective = "is_monthly_card_effective",
	growth_fund_expire_time = "growth_fund_expire_time",
	is_growth_fund_effective = "is_growth_fund_effective",
	rank_Merge = "rank_Merge",
	rank_Island = "rank_Island",
	latest_exit_time = "latest_exit_time",
	latest_exit_date = "latest_exit_date",
	first_game_end_time= "first_game_end_time",
	first_game_end_date = "first_game_end_date",
	team_time = "team_time",
}

UserAdd = {
	request_count = "request_count",	--累计联盟请求次数
	help_count = "help_count",			--累计联盟主动帮助次数
	requestGift_count = "requestGift_count",	--累计联盟申请帮助完成的领取次数
	giftPackags_count = "giftPackags_count",	--累计联盟充值礼包领取次数
	}

-- 公共属性
ThinkingKeyProperties = {
	event_scene1 = "event_scene1",
	event_scene2 = "event_scene2",
	is_payuser = "is_payuser",
 
	energy_A = "energy_A",
	energy_B = "energy_B",
	levels_A = "levels_A",
	levels_B = "levels_B",
	levels_process_A = "levels_process_A",
	levels_process_B = "levels_process_B",
	pay_time = "pay_time",
	total_login_days = "total_login_days",
	total_login_count = "total_login_count",
	daily_tasks = "daily_tasks",
	weekly_tasks = "weekly_tasks",
	beginner_tasks = "beginner_tasks",
	pay_user_A = "pay_user_A",
	pay_user_B = "pay_user_B",
	npc_unlock = "npc_unlock",
}

AdjustToken = {
	purPull = "kveiyx",
	purFail = "t135it",
	purchased = "4gk3cn",
	purTime2 = "oo6a43",
	purTime3 = "fqgu1t",
	purSumA = "n15oz3",
	purSumB = "joks1q",
	purSumC = "wamf49",
	battlePass = "ymeiuz",
	purSpeedFirst = "fndbfi",
	purEnergyFirst = "45gjh6",
	purExtraFirst = "1xyou3",
	level3 = "bfdbp9",
	level5 = "w8lxk0",
	level6 = "c0dri5",
	level7 = "ihej9y",
	level10 = "encgdw",
	level15 = "weoi1n",
	level20 = "rtjl40",
	accBind = "8e63u0",
	merge100 = "pg6myj",
	merge500 = "hjsz3h",
	adClick = "6z9axh",
	adSucc = "qa1p46",
	
	chest = "1nc17i",
	island = "6imht1",
	energy = "61jhmq",
	
	mergeFirst = "rzmafp",
	level2 = "lfqaeb",
	
	
	purSumD = "nzrn4t",
	purSumE = "vuza1p",
	
	level11 = "930rg5",
	level12 = "kgn7lo",
	level13 = "i1m8hk",
	level14 = "dptwmp",
	level16 = "g82p2s",
	level17 = "ctwjn8",
	level18 = "oh6vxd",
	level19 = "kmhqrg",
	}

local SdkHelper = Class()

function SdkHelper:Initialize()

    if not Game.IsUseTCPNet then
		return 
	end

    self.m_InstBridge = CS.LuaSdkHelper.Instance
    self.m_InstBridge:SDKInit(PaymentConfig:GetProducts())

    local webClientId = "470852906540-rqqigpn6qq0rsh5i0opjfnldq2k6j0hq.apps.googleusercontent.com"

    if Game.IsUseSDK then
		self.m_InstSDK = CS.GameSdkManager.Instance
		self.m_InstSDK:InitSdk()
       -- Log.Info("webClientId = " .. webClientId .. "\n" .. "irAppKey = " .. irAppKey)
        self.m_InstLogin = CS.SDKLoginModule.Instance
        self.m_InstLogin:InitSdk(UIMgr.layers[UILayerType.Lock], webClientId)
    end

end

function SdkHelper:GetLocalePrice()
	if self.m_InstBridge and self.m_InstBridge.GetStringByType then
		local tempPriceJson = self.m_InstBridge:GetStringByType(0)
		return tempPriceJson
	end
	return nil
end

function SdkHelper:InitializeAD()
	local irAppKey = ""
	local platformUnity = UE.Application.platform
	if platformUnity:Equals( UE.RuntimePlatform.Android ) then
		irAppKey = "ca-app-pub-2892001563789649/7068404133"
	elseif platformUnity:Equals( UE.RuntimePlatform.IPhonePlayer ) then
		irAppKey = "ca-app-pub-2892001563789649/7068404133"
	elseif platformUnity:Equals( UE.RuntimePlatform.WindowsEditor ) then
		irAppKey = "ca-app-pub-2892001563789649/7068404133"
	else
		irAppKey = "ca-app-pub-2892001563789649/7068404133"
	end
	if Game.IsUseADSDK then
		self.m_InstBridge:ADInit(irAppKey, true)
	end
end

--fun(firstState)
function SdkHelper:UserAgreement(fun)
    if self.m_InstLogin then
        self.m_InstLogin:ShowAcceptView(fun)
    end
end

--fun(bindState, token)
function SdkHelper:Login(fun)
    if self.m_InstLogin then
        self.m_InstLogin:Login(fun)
    end
end

--fun(state)
function SdkHelper:Binding(fun)
    if self.m_InstLogin then
        self.m_InstLogin:ShowBindingView(function (state)
			self:BindingResultProcesser(state)
			if fun then
				fun()
			end
		end)
    end
end

--更改账号登录
function SdkHelper:ChangeLogin(fun) 
	if self.m_InstLogin then
		self.m_InstLogin:ShowChangeLogin(fun)
	end
end

function SdkHelper:CancelAccount(fun)
	if self.m_InstLogin then
		self.m_InstLogin:CancelAccount(fun)
	end
end

--1 mail, 2 fb, 4 google, 8 ios
function SdkHelper:IsBinding()
    if self.m_InstLogin then
        return self.m_InstLogin:IsBind()
    end
    return 0
end

--直接绑定 不弹出UI
function SdkHelper:BindingDirect(bindType,fun)
	if self.m_InstLogin and self.m_InstLogin.BindWithCb then
		self.m_InstLogin:BindWithCb(bindType,function (state, bindState, token)
			self:BindingResultProcesser(state)
			if fun then
				fun(state)
			end
		end)
	end
end

function SdkHelper:BindingResultProcesser(state)
	NetTaskData:CheckTaskAdd(TaskFinishType.BindingAccount, SpecialId.BindingAccountTaskId)
	if state ~= 1 then
		local function onChanged()
			local strPath = UE.Application.persistentDataPath .. "/GameResData/Android/resver.bytes"

			local file = io.open(strPath, "w+")
			if file then
				file:write(CS.Rijndael.Encrypt("100"))
				file:close()
			end

			local function onOk()
				GameUtil:QuitGame()
			end
			self:UserSetOnce(UserSet.reg_time,TimeMgr:GetServerTime())
			UI_SHOW(UIDefine.UI_TipsTop, "Account is changed, Restart the Game?", onOk, nil, 2)
		end

		SceneMgr:SwitchScene(UISceneDefine.LoginScene, { 0, onChanged })
	else
		
	end
end

--payment
function SdkHelper:PayPay(orderNo,orderItem,payNum,userId,currencyType,orderSign,roleId)
   -- return self.m_InstBridge:SDKPay(orderNo,orderItem,payNum,userId,currencyType,orderSign,roleId)
end

--payment 俄罗斯第三方支付下单
function SdkHelper:ThirdPay(orderNo,orderItem,payNum,userId,currencyType,orderSign,roleId)
	do
		return
	end
	local thirdUrl = string.format("https://p-m-ea.q1.com/Payment/MOrderAdd?gameId=%s&pId=%s&orderItem=%s&orderNo=%s&payNum=%s&userId=%s&orderSign=%s&serverId=1&actorId=%s&currencyType=%s&bankCode=%s&payApproachCode=%s&countryCode=%s&jsonCallback=json",
	 "2059","2059006",orderItem,orderNo,payNum,userId,orderSign,roleId,
 	currencyType,"payermax","BANK_TRANSFER","RU")

	local tFunReqBack = function(errorState, reqCode, strResult)
		if errorState == 1 then
			Log.Error("###### NetErr : ", thirdUrl)
			UI_UPDATE(UIDefine.UI_Payment, 1029, 1, nil)
			return
		end
	
		local resData = Json.decode(strResult)
		if resData == nil then
			Log.Error("###### thirdStr json err", thirdUrl)
			UI_UPDATE(UIDefine.UI_Payment, 1029, 1, nil)
			return
		end
		if type(resData) == "table" then
			local h5Url = resData.url
			if h5Url then
				CS.UnityEngine.Application.OpenURL(h5Url);
				--self.m_InstBridge:OpenH5Url(h5Url,1)
			end
		end
		UI_UPDATE(UIDefine.UI_Payment, 1029, 1, nil)
	end
	_G.CS.HttpMono.Instance:HTTPGet(thirdUrl, tFunReqBack)
	return 0
end
--payment
function SdkHelper:Q1Pay(orderNo,orderItem,payNum,userId,currencyType,orderSign,roleId,paycode_ios)

	--// com.q1.sample.18 是你Google商店创建的应用商品id
	--// 商品ID*单价（分）*数量*商品名称*苹果或GooglePlay内购时要多加上内购定义商品ID
	--// String orderItem = "1*100*1*dlb*com.q1.sample.18";
	--// try {
	--//     orderItem = URLEncoder.encode(orderItem, "UTF-8");
	--// } catch (UnsupportedEncodingException e) {
	--//     orderItem = "1*100*1*dlb*com.q1.game.id";
	--// }

	--// // 签名规则看服务器接入文档
	--// String orderSign = "71496f090f3793eed5b6ad0340439b3e";
	--// String orderNo = "637196982041824507";

	--// // 这个的值，一般为orderItem里面的 单价/100
	--local money = "1";
	----// // 货币类型，遵守国际货币缩写格式
	--local currencyType = "USD";
	----// String userId = "1000001";
	--local userId = NetUpdatePlayerData:GetPlayerID();
	--local roleId = "1001";
	if RedDiamondManager:IsOpenWebPay() and self.m_InstBridge.OpenH5Url then
		return self:ThirdPay(orderNo,orderItem,payNum,userId,currencyType,orderSign,roleId)
	end
	local serverId = 1;

	local v = UE.PlayerPrefs.GetInt("test_server_url_id")
	if v > 0 then
		serverId = 99
	end

	return self.m_InstBridge:Q1SDKPay(orderItem,roleId,orderNo,orderSign,currencyType,userId,serverId,payNum,paycode_ios)
end

--payment
function SdkHelper:Q1PaySub(orderNo,orderItem,payNum,userId,currencyType,orderSign,roleId,offerId)
	local serverId = 1;
 
	 --if self.m_InstBridge:Q1SDKPaySub == nil then return end
	--如何判断这个玩家没有这个方法。
	return self.m_InstBridge:Q1SDKPaySub(orderItem,roleId,orderNo,orderSign,currencyType,userId,serverId,payNum,offerId)
end

--app flyer
function SdkHelper:AFEvent(eName, eParam)
    if self.m_InstSDK then
        self.m_InstSDK:SDKAFEvent(eName, eParam)
    end
end

function SdkHelper:AFEventPay(price, payId, orderId)
    if self.m_InstSDK then
        self.m_InstSDK:Pay(price, payId, orderId)
    end
end

function SdkHelper:AFEventPayPurchase(price, payId, orderId)
    if self.m_InstSDK then
        self.m_InstSDK:PayPurchase(price, payId, orderId)
    end
end

function SdkHelper:AFEventLogin()
    if self.m_InstSDK then
        self.m_InstSDK:Login()
    end
end

function SdkHelper:AFEventRegister(name)
    if self.m_InstSDK then
        self.m_InstSDK:Register(name or "someone")
    end
end

function SdkHelper:FireBaseEventPayPurchase(price, payId, orderId)
    if self.m_InstSDK then
        local classRef = CS.Firebase.Analytics.FirebaseAnalytics
        local eventName = classRef.EventPurchase

        local strV = ""

        local k0 = classRef.ParameterCurrency
        local v0 = "USD"
        strV = strV .. k0 .. "|" .. v0 .. "|" .. "string" .. ";"

        local k1 = classRef.ParameterValue
        local v1 = tostring(price)
        strV = strV .. k1 .. "|" .. v1 .. "|" .. "double" .. ";"

        local k2 = classRef.ParameterTransactionId
        local v2 = tostring(orderId)
        strV = strV .. k2 .. "|" .. v2 .. "|" .. "string" .. ";"

        local k3 = classRef.ParameterCoupon
        local v3 = tostring(payId)
        strV = strV .. k3 .. "|" .. v3 .. "|" .. "string"

        Log.Info("fire base purchase : ", strV)
        self.m_InstSDK:SDKFireMultipleEvent(eventName, strV)
    end
end

function SdkHelper:InitBugly(appId, userId, isDebug)
    --if self.m_IsInitBugly then
    --    return
    --end
    --self.m_IsInitBugly = true

    --appId = appId or "0a5024c304"
    --userId = userId or "good"
    --isDebug = isDebug or false
    --
    --self.m_InstBridge:InitBuglySDK(appId, userId, isDebug)
end

----
function SdkHelper:ADMovieState(adId,adUnityType)
    if Game.IsUseADSDK then
        return self.m_InstBridge:ADMovieState(adId,adUnityType)
    end
    return true
end

function SdkHelper:ADMoviePlay(adId,adUnitType)
	self.m_InstBridge:ADMoviePlay(adId,adUnitType)
end

function SdkHelper:AddPushNotices(str)
    self.m_InstBridge:AddPushNotices(str)
end


function SdkHelper:AddRepeatPushTimer(str)
	self.m_InstBridge:AddRepeatPushTimer(str)
end

function SdkHelper:RemovePushNotices(str)
    self.m_InstBridge:RemovePushNotices(str)
end

function SdkHelper:RemoveAllPushNotices()
    self.m_InstBridge:RemoveAllPushNotices()
end

function SdkHelper:GOOGLE_ADV_ID()
    local str = self.m_InstBridge:GetGoogleAAID()
    if string.empty(str) then
        return "nil"
    end
    return str
end

function SdkHelper:InputGuideInfo(str)
    if Game.IsUseSDK then
        return
    end

    self.m_InstBridge:InputGuideInfo(str)
end

function SdkHelper:Copy(str)
    self.m_InstBridge:Copy(str)
end

function SdkHelper:Q1LevelUp(level)
	if self.m_InstSDK then
		self.m_InstSDK:Q1LevelUp(1,NetUpdatePlayerData:GetPlayerID(),NetUpdatePlayerData:GetPlayerName(), NetUpdatePlayerData:GetLevelMess())
	end
end

function SdkHelper:Q1RoleLogin()
	if self.m_InstSDK then
		self.m_InstSDK:Q1RoleLogin(1,NetUpdatePlayerData:GetPlayerID(),NetUpdatePlayerData:GetPlayerName(), NetUpdatePlayerData:GetLevelMess())
	end
end

function SdkHelper:ThinkingTrackEvent(eventName,param)
	--if eventName == "Home" then
		--Log.Info("xxx",param)
	--end
	--if param ~= nil then 
		--Log.Error("数数埋点：", eventName ," :   ", table.dump(param))
	--else
		--Log.Error("数数埋点：", eventName)
	--end
	if self.m_InstSDK then
		self.m_InstSDK:TrackEvent(eventName,param)
	end
end

function SdkHelper:UserSet(key,param)
	if key == nil or param == nil then
		Log.Error("UserSet  nil" .. key)
		return
	end
	if self.m_InstSDK then
		self.m_InstSDK:UserSet(key,param)
	end
end

function SdkHelper:UserSetOnce(key, param)
	if key == nil or param == nil then
		Log.Error("UserSetOnce  nil" .. key)
		return
	end
	if Game.CompareNeedAppVer("1.0.2") then
		if self.m_InstSDK then
			self.m_InstSDK:UserSetOnce(key,param)
		end
	end
end

function SdkHelper:UpdateSuperProperties(key,param)
	if key == nil or param == nil then
		Log.Error("UpdateSuperProperties  nil" .. key)
		return 
	end
	if self.m_InstSDK then
		self.m_InstSDK:UpdateSuperProperties(key,param)
	end
end

function SdkHelper:StartDownloadSilent(fun)
	if self.m_InstBridge then
		self.m_InstBridge:StartDownloadSilent(fun)
	end
end

function SdkHelper:StartDownloadSilentByMap(keys,fun)
	if self.m_InstBridge then
	self.m_InstBridge:StartDownloadSilentByMap(keys,fun)
	end
end

function SdkHelper:StartDownloadSilentByMapAndUI(keys,fun,isShow)
	if self.m_InstBridge and self.m_InstBridge.StartDownloadSilentByMapAndUI then
		self.m_InstBridge:StartDownloadSilentByMapAndUI(keys,fun,isShow)
		return true
	end
	return false
end

ThinkingName = {
	["UI_DailyTask"]="每日/每周/新手任务",
	["worker"] = "资源采集",
	["payment"] = "付费商城",
	["FlashBuy"] = "闪购礼包",
	["GrowthFund"] = "成长基金",
	["GrowthFundPay"] = "成长基金付费",
	["MonthCard"] = "月卡",
	["ActToy"] = "玩具活动",
	["SavingBank"] = "存钱罐",
	["UI_LvUP"] = "主营地升级",
	["UI_ZooLvUP"] = "动物园升级",
	["RecoveryEnergy"] = "体力恢复",
	["UI_AD_0"] = "广告奖励",
	["TinyGame"] = "小游戏",
	["Shop"] = "商店",
	["UI_BuyGold"] ="商店",
	["UI_Collection"] = "图鉴",
	["UI_Depot"] = "商店",
	["UI_DialogView"] = "任务奖励-合成",
	["UI_DungeonEnergy"] = "体力商城",
	["UI_Endless"] = "无尽的奖励",
	["UI_Energy"] = "体力商城",
	["UI_FirstGift"] = "首充礼包",
	["UI_Rank"] = "营地排行榜",
	["RankOneReward"] = "营地排行榜",
	["UI_RankLimit"] = "副本排行榜",
	["UI_THReward"] = "购买通行证",
	["UI_DiyGift"] = "购买自选礼包",
	["UI_FlowerCompetition"] = "鲜花比拼",
	["UI_End2less"] = "无尽的奖励2",
	

	-----补充--------
	["UI_EasterEgg"] = "砸蛋活动",
	["UI_BuyPassPort"] = "VIP通行证",
	["seasonTask"] = "季节活动岛屿任务",
	["UI_MaxReward"] ="副本任务完成",
	["UI_NewItem"] = "解锁新物品奖励",
	["NewItems"] =  "奖励结算",
	["UI_FriendView"] = "好友系统",
	["UI_Redemption"] = "兑换码",
	["creat_union"] = "创建联盟",
	["union_gift"]="联盟充值礼包",
	["union_help"]="帮助联盟请求",
	["union_help_complete"]="联盟订单完成",
	["UI_PrivilegeCard"] = "特权月卡",
	  
	
	-----补充2-----------
	["Combine"] = "合成新物品",
	["Group"] ="解锁云区",
	["UseTypeResource"] = "使用资源道具",
	["Build"] = "建造物品",
	["BigTree"] = "资源采集",
	["CutDown"] = "资源采集",
	
	
	["objFloater"] = "漂浮物",
	["fly"] = "漂浮物",
	["Producer"] ="枯萎采集物",
	["GiftPack"] = "每日礼包",
	["UI_MailPanel"] = "邮件",
	["BowlingStepReward"]="联盟活动",
	["UI_Appraise"] = "评价奖励",
	["UI_EnergyLottery"]= "体力抽奖",
	["UI_NewItemOtherReward"] = "解锁新物品奖励",
	["UI_THFinish"] = "副本活动阶段奖励",
	["UI_TreasureHunt"] = "副本通行证",
	["DoubleOre"] = "采集活动双倍矿",

	["BowlingLeagueRankReward"] = "联盟活动联盟排名",
	["BowlingStepReward"] = "联盟活动积分奖励",
	["BowlingMemberRankReward"] = "联盟活动成员排名",
	["BuyAiWorker"] = "买智能工人时间",
	
	["UI_HalloweenCollection"] = "万圣节图鉴领取",
	["HalloweenConsumePower"] = "采集活动万圣节矿",
	["UI_MonthlySeason"] = "月度赛季",
	["funnyworker"] = "采集玩具矿",
	["funnymine"] = "购买玩具矿",
	["racing_changereward"] = "racing_changereward",--滑雪重选节点
	["goldpass_buypoints"] = "goldpass_buypoints",--月度买通行证
	["DailyTarget"] = "DailyGoals",
	["UI_ContinuousRecharge"] = "连续充值",
	["LuckOre"] = "幸运神矿",
	["ReduceItem"] = "后台扣除资源",
	["redDiamondRecharge"] = "红钻充值领奖",
	--["UI_OrderBuyMaterialTip"] =""
	--["UI_OrderBuyMaterialTipSpecial"] =
}
--4001
ThinkingType = {
	["UI_DailyTask"]= 4002,--"每日/每周/新手任务",
	["worker"] = 4001,--"资源采集",
	["payment"] = 4003,--"付费商城",
	["FlashBuy"] = 4002,--"闪购礼包",
	["GrowthFund"] = 4002,--"成长基金",
	["GrowthFundPay"] = 4002,--"成长基金",
	["MonthCard"] = 4002,--"月卡",
	["ActToy"] = 4006,--"玩具活动",
	["SavingBank"] = 4006,--"存钱罐",
	["UI_LvUP"] = 4001,--"主营地升级",
	["UI_ZooLvUP"] = 4001,--"动物园升级",
	["RecoveryEnergy"] = 4005,--"体力恢复",
	["UI_AD_0"] = 4005,--"广告奖励",
	["TinyGame"] = 4004,--"小游戏",
	["Shop"] = 4003,--"商店",
	["UI_BuyGold"] =4003,--"商店",
	["UI_Collection"] = 4001,--"图鉴",
	["UI_Depot"] =4003,--"商店",
	["UI_DialogView"] = 4001,--任务奖励-合成
	["UI_DungeonEnergy"] = 4003,--体力商城
	["UI_Endless"] = 4006,--"无尽的奖励",
	["UI_Energy"] = 4003,--"体力商城",
	["UI_FirstGift"] = 4002,--"首充礼包",
	["UI_Rank"] = 4005,--"营地排行榜",
	["RankOneReward"] = 4005,--营地排行榜
	["UI_RankLimit"] = 4005,--"副本排行榜",
	["UI_THReward"] = 4002,--购买通行证
	["UI_DiyGift"] = 4006, --购买自选礼包
	["UI_FlowerCompetition"] = 4006, --鲜花比拼
	["UI_FriendView"] = 4001,
	["UI_End2less"] = 4006,--"无尽的奖励2",
	
	-----补充--------
	["UI_EasterEgg"] = 4006,
	["UI_BuyPassPort"] = 4006,
	["seasonTask"] = 4002,
	["UI_MaxReward"] = 4006,
	["UI_NewItem"] = 4005,
	["NewItems"] =  4005,          --界面名字：UI_NewItems
	["UI_Redemption"] = 4005,
	["creat_union"] = 4005,
	["union_gift"]=4005,
	["union_help"]=4005,
	["union_help_complete"]=4005,
	["UI_PrivilegeCard"] = 4005,
	
	
	-----补充2-----------
	["Combine"] = 4001,
	["Group"] = 4001,
	["UseTypeResource"] = 4005,
	["Build"] = 4005,
	["BigTree"] = 4005,
	["CutDown"] = 4005,


	["objFloater"] = 4002,
	["fly"] = 4002,
	["Producer"] =4005,
	["GiftPack"] = 4002,
	["UI_MailPanel"] = 4005,
	["UI_Appraise"] = 4005,
	["UI_EnergyLottery"]= 4002,
	["UI_NewItemOtherReward"] = 4001,
	["UI_THFinish"] = 4005,
	["UI_TreasureHunt"] = 4005,
	["DoubleOre"] = 4006,

	["BowlingLeagueRankReward"] = 4007,
	["BowlingStepReward"] = 4007,
	["BowlingMemberRankReward"] = 4007,
	
	["BuyAiWorker"] = 4008,
	
	["UI_HalloweenCollection"] = 4006,
	["HalloweenConsumePower"] = 4006,
	["UI_MonthlySeason"] = 4006,
	["funnyworker"] = 4006,
	["funnymine"] = 4006,
	["DailyTarget"] = 4005,
	["UI_ContinuousRecharge"] = 4006,
	["LuckOre"] = 4006,
	--["UI_OrderBuyMaterialTip"] =
	--["UI_OrderBuyMaterialTipSpecial"] =
}
function SdkHelper:GetNameAndTypeByUI(uiname)
	local name = uiname or "other"
	local type
	if ThinkingName[uiname] ~= nil then
		name = ThinkingName[uiname]
	else
		name = "其他"
	end
	
	if ThinkingType[uiname] ~= nil then
		type = ThinkingType[uiname]
	else
		type = 4005
	end
	return name,type
end

-- 退出设置界面时。
function SdkHelper:DestroySDKView()
	if self.m_InstLogin then
	--return self.m_InstLogin:DestroySDKView()
		local value = self.m_InstLogin:DestroySDKView()
		return value
	end
 	return false
end


function SdkHelper:ThinkingLogin()
	if self.m_InstSDK then
		self.m_InstSDK:ThinkingLogin()
	end
end


function SdkHelper:UserAdd(eventName,param)
	if self.m_InstSDK then
		self.m_InstSDK:UserAdd(eventName,param)
	end
end

function SdkHelper:CanShowAdsConsentForm()
	if self.m_InstBridge.CanShowAdsConsentForm then
		return self.m_InstBridge:CanShowAdsConsentForm()
	end
	return false
end

function SdkHelper:ShowAdsConsentForm()
	if self.m_InstBridge.ShowAdsConsentForm then
		self.m_InstBridge:ShowAdsConsentForm()
	end
end

function SdkHelper:AIHelpQ1_updateUserInfo()
	if self.m_InstBridge and self.m_InstBridge.AIHelpQ1_updateUserInfo then
		self.m_InstBridge:AIHelpQ1_updateUserInfo()
		return true
	end
	return nil
end
function SdkHelper:AIHelpQ1_showWithApiConfig()
	if self.m_InstBridge and self.m_InstBridge.AIHelpQ1_showWithApiConfig then
		local id = "E001"
		local msg = ""
		if Game.IsTinyGame then
			 
		elseif Game.IsNativeTinyGame then
			id = "E001"
		end
		self.m_InstBridge:AIHelpQ1_showWithApiConfig(id,msg)
		NetContactData:ClearPushMsgTag()
		return true
	end
	return nil
end

function SdkHelper:AIChatHelpAndShow()
	-- do
	-- 	return
	-- end
	if _G.CS.StartGame.Instance and _G.CS.StartGame.Instance.GetSDKToken then
		local sdkToken = _G.CS.StartGame.Instance:GetSDKToken()
		local HOST = "https://kefu.you03.com"
		local path = "/aichat/index.html?"
		local SECRET = "Uz7NbStqvBx6WthB"

		local loginType = 0
		local platFormType = 1
		local mainType = 0
		local gameId = GAME_APP_ID
		local uld = _G.CS.StartGame.Instance:GetUserId()
		local actorId = v2s(NetUpdatePlayerData:GetPlayerID())
		local sign = GameUtil.MD5String(loginType..platFormType..mainType..gameId..actorId..SECRET)

		local str = string.format("gameId=%s&loginType=%s&platFormType=%s&mainType=%s&sign=%s&actorId=%s&sdkToken=%s",gameId,loginType,platFormType,mainType,sign,actorId,sdkToken)
		local url = HOST..path..str
		Log.Pink("SdkHelper aichat url=" .. url)
		--CS.UnityEngine.Application.OpenURL(url)
		if CS.LuaSdkHelper.Instance.OpenH5Url then
			CS.LuaSdkHelper.Instance:OpenH5Url(url, false, 2, 1, false)
		end
		return true
	end
	return nil
end

function SdkHelper:GetLocalePriceAmount()
	if self.m_InstBridge and self.m_InstBridge.GetStringByType then
		local tempPriceJson = self.m_InstBridge:GetStringByType(1)
		return tempPriceJson
	end
	return nil
end

function SdkHelper:FacebookShareUrl(url)
	if self.m_InstBridge and self.m_InstBridge.FacebookShareUrl then
		self.m_InstBridge:FacebookShareUrl(url)
	end
end

function SdkHelper:ShowPrivacyProtocol(privacyId)
	if self.m_InstSDK and self.m_InstSDK.ShowPrivacyProtocol then
		self.m_InstSDK:ShowPrivacyProtocol(privacyId)
	end
end

return SdkHelper