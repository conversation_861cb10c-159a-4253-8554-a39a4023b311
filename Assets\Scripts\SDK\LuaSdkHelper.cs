using LitJson;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using XLua;

public class LuaSdkHelper
{
    private static LuaSdkHelper s_Instance = null;
    public static LuaSdkHelper Instance
    {
        get
        {
            if (s_Instance == null)
            {
                s_Instance = new LuaSdkHelper();
            }
            return s_Instance;
        }
    }

    string priceJson = "";
    string priceAmount = "";
    public LuaSdkHelper()
    {

    }

    public byte[] LoadPB()
    {
        //string tt =  AssetManager.Instance.LoadTextAssetSync("Proto/proto.bytes");
        return FileUtility.SafeReadAllBytes(Path.Combine(Application.dataPath, "Resources/proto.bytes"));
    }
    
    //On back
    public void OnSDKPay(int code) //, string productId, string orderId, string receipt
    {
        try
        {
            string callBackStr = string.Format("PayFromCSharp({0})", code); //
                                                                            //  Debug.Log(callBackStr);
            LuaManager.Instance.CallLuaDoString(callBackStr);
        }
        catch (Exception e)
        {
            Debug.LogWarning("###### OnSDKPay : " + e.Message);
        }
    }

    // Pay
    public int SDKPay(string productId, string orderId)
    {
        //TODO 暂时作弊充值。
        //#if UNITY_EDITOR
        OnSDKPay(-8888); //, productId, orderId, "{name:\"test\"}"
        return 0;
        // #else
        //                 //         return IAPSystem.Instance.Pay(productId, orderId);
        // #endif



    }
    public int Q1SDKPay(string orderItem, string roleId, string orderNo, string orderSign, string currencyType, string userId, int serverId, string money, string payCode)
    {
        //TODO 暂时作弊充值。
        LogMan.Info("充值调用接口" + orderItem);
        GameSdkManager.Instance.SDKAFEvent("kveiyx", "purPull");

#if UNITY_EDITOR
        // OnSDKPay(-8888, productId, orderId, "{name:\"test\"}");
        OnSDKPay(-8888);
#elif UNITY_ANDROID

        Q1SdkMgr.Instance.Pay(orderItem,roleId,orderNo,orderSign,money,currencyType, StartGame.Instance.GetUserId(),serverId);
#elif UNITY_IOS
        Q1SdkMgr.Instance.IOS_Pay(payCode,roleId,serverId.ToString(),
            userId,money, orderItem,orderNo,currencyType,orderSign);
                 //       return IAPSystem.Instance.Pay(productId, orderId);
#endif

        return 0;

    }

    public int Q1SDKPaySub(string orderItem, string roleId, string orderNo, string orderSign, string currencyType, string userId, int serverId, string money, string offerId,string payCode)
    {
        //TODO 暂时作弊充值。
        LogMan.Info("充值调用接口" + orderItem);

#if UNITY_EDITOR
        // OnSDKPay(-8888, productId, orderId, "{name:\"test\"}");
        OnSDKPay(-8888);
#elif UNITY_ANDROID
        Q1SdkMgr.Instance.PaySub(orderItem,roleId,orderNo,orderSign,money,currencyType, StartGame.Instance.GetUserId(),serverId,offerId);
                 //       return IAPSystem.Instance.Pay(productId, orderId);
#elif UNITY_IOS
   Q1SdkMgr.Instance.IOS_SubPay(payCode,roleId,serverId.ToString(),
            userId,money, orderItem,orderNo,currencyType,orderSign);
#endif
        return 0;

    }
    // Account
    public void SDKInit(string payInf)
    {
#if UNITY_EDITOR
#else
       // IAPSystem.Instance.Initialize(payInf.Split(';'));
#endif

#if UNITY_ANDROID && !UNITY_EDITOR
        Q1SdkMgr.Instance.QueryProductsAsync();
#endif

    }

    public void ADInit(string appkey, bool isDebug)
    {
        AdsManager.Instance.InitAds(appkey, isDebug);

        AdsManager.Instance.RewardedVideoClickedEvent += ADMovieBackClick;
        AdsManager.Instance.RewardedVideoAdGetRewardedEvent += ADMovieBackWin;
        AdsManager.Instance.RewardedVideoErrorEvent += ADMovieBackErr;
    }

    public void ShowAdsConsentForm()
    {
        // AdsManager.Instance.ShowAdsConsentForm();
    }

    public bool CanShowAdsConsentForm()
    {
        // return AdsManager.Instance.CanShowAdsConsentForm();
        return false;
    }

    public bool ADMovieState(int adId, string adUnityType)
    {
#if UNITY_EDITOR
         return true;
#else
        // return AdsManager.Instance.IsRewardedVideoAvailability(adUnityType);
        return true;
#endif
    }

    public int ADMoviePlay(int adId, string adUnityType)
    {
        AdsManager.Instance.ShowRewardedVideo(adId, adUnityType);
        // if (!StartGame.Instance.IsSDK)
        // {
        //     ADMovieBackWin(adId);
        // }
        return 0;
    }

    public void ADMovieBackErr(int adId, int code, string des)
    {
        try
        {
            string callBackStr = string.Format("ADFromCSharp({0}, {1}, {2})", -1, adId, code);
            LuaManager.Instance.CallLuaDoString(callBackStr);
        }
        catch (Exception e)
        {
            Debug.LogWarning("###### ADMovieBackErr : " + e.Message);
        }
    }

    public void ADMovieBackClick(int adId)
    {
        try
        {
            string callBackStr = string.Format("ADFromCSharp({0}, {1}, {2})", 1, adId, 0);
            LuaManager.Instance.CallLuaDoString(callBackStr);
        }
        catch (Exception e)
        {
            Debug.LogWarning("###### ADMovieBackClick : " + e.Message);
        }
    }

    public void ADMovieBackWin(int adId)
    {
        try
        {
            string callBackStr = string.Format("ADFromCSharp({0}, {1}, {2})", 0, adId, 0);
            LuaManager.Instance.CallLuaDoString(callBackStr);
        }
        catch (Exception e)
        {
            Debug.LogWarning("###### ADMovieBackWin : " + e.Message);
        }
    }

    public string GetGoogleAAID()
    {
        return SDKUtility.GetAdvertisingID();
    }
        public void AddRepeatPushTimer(string param)
        {
                if (string.IsNullOrEmpty(param)) return;
                string[] sp = param.Split(';');
                foreach (string sub in sp)
                {
                        string[] arr = sub.Split('|');
                        if (arr.Length == 5)
                        {
                                if (int.TryParse(arr[0], out int uid) && int.TryParse(arr[3], out int hourstamp) && int.TryParse(arr[4], out int minstamp))
                                        GameSdkManager.Instance.AddRepeatPushTimer(uid, arr[1], arr[2], hourstamp, minstamp);
                        }
                }
        }
  

    public void InputGuideInfo(string uid)
    {
        SDKUtility.InputGuideInfo(uid);
    }

    public void AddPushNotices(string param)
    {
        if (string.IsNullOrEmpty(param)) return;
        string[] sp = param.Split(';');
        foreach (string sub in sp)
        {
            string[] arr = sub.Split('|');
            if (arr.Length == 4)
            {
                if (int.TryParse(arr[0], out int uid) && long.TryParse(arr[3], out long timestamp))
                    GameSdkManager.Instance.AddOncePushTimer(uid, arr[1], arr[2], timestamp);
            }
        }
    }

    public void RemovePushNotices(string param)
    {
        if (string.IsNullOrEmpty(param)) return;
        string[] sp = param.Split('|');
        foreach (string sub in sp)
        {
            if (int.TryParse(sub, out int uid))
                GameSdkManager.Instance.RemovePushTimer(uid);
        }
    }

    //目前noticeId会相同，在失去焦点时，清理一下消息管理器
    public void RemoveAllPushNotices()
    {
        GameSdkManager.Instance.RemoveAllNotifiID();
    }

    public void Copy(string str)
    {
        GameSdkManager.Instance.Copy(str);
    }

    public void StartDownloadSilent(Action<bool> backFun)
    {
        GameSilentResource.Instance.DownloadSilent(backFun);
    }
        public void StartDownloadSilentByMapAndUI(List<string> keys, Action<bool> backFun,bool isShow)
        {
                GameSilentResource.Instance.StartDownloadSilentByMapAndUI(keys, backFun,isShow);
        }

    public void StartDownloadSilentByMap(List<string> keys, Action<bool> backFun)
    {
        GameSilentResource.Instance.DownloadSilentByMap(keys, backFun);
    }

    public bool CheckHasFile(string FileName)
    {
        return AssetBundles.AssetBundleConfig.CheckHasFile(FileName);

    }

        public float GetParticleStartSizeConstant(ParticleSystem particle)
        {
            return particle.main.startSize.constant;
        }
        public void SetParticleStartSizeMinMaxCurve(ParticleSystem particle, float x, float y)
        {
            var main = particle.main;
            main.startSize = new ParticleSystem.MinMaxCurve(x, y);
        }
        public Mesh NewTgMesh()
            {
                return new Mesh();
        }
    public void SetPriceJson(string str)
    {
        Debug.Log("SetPriceJson:" + str);
        priceJson = str;
    }
    public void SetPriceJsonAmount( string priceAmount)
    {
        Debug.Log("SetPriceJson: SetPriceJsonAmount： " + priceAmount);
        this.priceAmount = priceAmount;
    }

    public string GetStringByType(int type = 0)
    {
        if(type == 0)
        {
            return priceJson;
        }
        if(type == 1)
        {
            return priceAmount;
        }
        return string.Empty;
    }
   public void Q1HelperCount(int count)
    {
        try
        {
            string callBackStr = string.Format("AIHelpFromCSharp({0})", count);
            LuaManager.Instance.CallLuaDoString(callBackStr);
        }
        catch (Exception e)
        {
            Debug.LogWarning("###### AIHelpFromCSharp : " + e.Message);
        }
    }
    public void AIHelpQ1_updateUserInfo()
    {
        Q1SdkMgr.Instance.AIHelpQ1_updateUserInfo(StartGame.Instance.GetUserId(),StartGame.Instance.GetRoleId());
    }
    public void AIHelpQ1_showWithApiConfig(string entranceId,string welcomeMessage)
    {
        Q1SdkMgr.Instance.AIHelpQ1_showWithApiConfig(entranceId,welcomeMessage);
    }

    public void OpenH5Url(string url, bool closeable, int screenType, int windowType, bool isFullScreen)
    {
        Q1SdkMgr.Instance.Q1_OpenH5Url(url, closeable, screenType, windowType, isFullScreen);
    }

    public void FacebookShareUrl(string url)
    {
        Q1SdkMgr.Instance.FacebookShareUrl(url);
    }

    public string GetRoleId()
    {
        return StartGame.Instance.GetRoleId() ?? "0";
    }

    public string GetGameUserId()
    {
        return StartGame.Instance.GetUserId() ?? "0";
    }

    public int GetServerId()
    {
        return Q1SdkMgr.Instance.pServerId;
    }

    public string GetUserName()
    {
        return Q1SdkMgr.Instance.GetUserName();
    }

    public string GetPID()
    {
        return Q1SdkMgr.Instance.GetPID();
    }
}
