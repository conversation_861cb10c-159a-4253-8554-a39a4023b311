using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;
using Com.Q1.Sdk.Unity;
using Com.Q1.Sdk.Unity.Internal.Data;
using UnityEngine;
using UnityQ1SDK_Common;
using Debug = UnityEngine.Debug;
using SystemInfo = UnityEngine.Device.SystemInfo;

public class Q1SdkMgr : SDKSingleton<Q1SdkMgr>,IQ1InitCallback
{

    private UserData m_UserData;
    
    public string pRoleId;//角色ID
    public string pRoleName;//角色名称
    public int pRoleLevel;//角色等级
    public int pVipLevel;//VIP等级
    public string pGameUserId;//游戏用户ID
    public int pServerId;//服务器ID
    public string pServerName;//服务器名称
    public int pTotalRevenue;//总收入
    public long pRoleCreateTime;//角色创建时间
    public long pRoleLevelTime;//角色等级时间

    private bool m_IsLogout = false;
    
#if UNITY_IPHONE
    private bool m_IsPrivacyAgreed = false;
#endif
    
    /// <summary>
    /// 是否同意了协议
    /// ios 100% 有同意协议回调 使用返回结果
    /// android 100% 没有同意协议回调 默认true（不同意进不了游戏）
    /// </summary>
    private bool IsPrivacyAgreed {
        get
        {
            #if UNITY_IPHONE
                return m_IsPrivacyAgreed;
            #endif
            return true;
        }
    }

    public bool IsLogicReadyLogin;
    

    public void InitQ1Sdk()
    {
        Trace("InitQ1Sdk()");
#if UNITY_ANDROID
        Q1SDKAdapterCommon.Instance.InitAdapter(EArea.Mainland, EPlatform.Android, this);
#elif UNITY_IPHONE
        Q1SDKAdapterCommon.Instance.InitAdapter(EArea.Mainland, EPlatform.iOS, this);
#elif UNITY_STANDALONE_WIN
        Q1SDKAdapterCommon.Instance.InitAdapter(EArea.Mainland, EPlatform.Windows, this);
#else
        Q1SDKAdapterCommon.Instance.InitAdapter(EArea.Mainland, EPlatform.Common, this);
        
#endif
        
        
        ThingkingAnalyticsMgr.Instance.TrackEvent(ThinkingKey.init_sdk);
    }

    public void TryLogin()
    {
        Trace("TryLogin()");

        if (!IsLogicReadyLogin)
        {
            Trace("Not logic ready yet, waiting for the logic.");
            return;
        }
        
        if (!IsPrivacyAgreed)
        {
            Trace("Not agreed yet, waiting for the agreement.");
            return;
        }
        
        Login();
    }
    
    public void Login()
    {
        ThingkingAnalyticsMgr.Instance.TrackEvent(ThinkingKey.login_start);
        if (m_IsLogout)
        {
            m_IsLogout = false;
            Trace("ReLogin()");
            Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000100003);
        }
        else
        {
            Trace("Login()");
            Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000100001);    
        }
        
    }
    
    public void Logout()
    {
        Trace("Logout()");
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000100002);
        m_IsLogout = true;
    }

    /// <summary>
    /// 注销账号
    /// </summary>
    public void CancelAccount()
    {
        Trace("CancelAccount()");
        ReqShowRegulatoryData data = new ReqShowRegulatoryData()
        {
            Type = RegulatoryType.ShowLogoffPage
        };
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000100015, data);
        
    }
    
    public void Bind(int platform)
    {
        Debug.Log("binding " + platform);

    }

    // com.q1.sample.18 是你Google商店创建的应用商品id
    // 商品ID*单价（分）*数量*商品名称*苹果或GooglePlay内购时要多加上内购定义商品ID
    //        com.q1.slots.18
    // String orderItem = "1*100*1*dlb*com.q1.sample.18";
    // try {
    //     orderItem = URLEncoder.encode(orderItem, "UTF-8");
    // } catch (UnsupportedEncodingException e) {
    //     orderItem = "1*100*1*dlb*com.q1.sample.18";
    // }
    // String roleId = "1001";
    // // 签名规则看服务器接入文档
    // String orderSign = "741344053143483a94590062db9c0a61";
    // String orderNo = "637348140448148306";
    // //payParams.orderNo = System.currentTimeMillis() + "" + System.currentTimeMillis();
    // //payParams.orderSign = Utils.orderSign(payParams);

    //  Q1Sdk.sharedInstance().setPayType(CommConstants.PAY_TYPE_GOOGLE);
    // // 这个的值，一般为orderItem里面的 单价/100
    // String money = "1";

    // // 货币类型，遵守国际货币缩写格式
    // String currencyType = "USD";
    // String userId = "1000001";
    // int serverId = 1;

    public void Pay(string orderItem,
                       string roleId,
                       string orderNo,
                       string orderSign,
                       string money,
                       string currencyType,
                       string userId,
                       int serverId)
    {
        Trace("Pay()");

        var log = string.Format(@"
    OrderItem: {0}
    RoleId: {1}
    OrderNo: {2}
    OrderSign: {3}
    Money: {4}
    CurrencyType: {5}
    UserId: {6}
    ServerId: {7}",
            orderItem, roleId, orderNo, orderSign, money, currencyType, userId, serverId);
        Trace("=======================begin");
        Trace(log);
        Trace("=======================end");
        

        Com.Q1.Sdk.Unity.Internal.Data.ReqPayData pay = new Com.Q1.Sdk.Unity.Internal.Data.ReqPayData()
        {
            UserId = userId,
            Money = money.ToString(),
            ServerId = serverId.ToString(),
            OrderItem = orderItem,
            
#if UNITY_IPHONE || UNITY_IOS
            ProductId = "com.q1.sdk.product_1", // no need in Android 
            ProductName = "com.q1.sdk.product_1", // no need in Android
            CurrencyType = currencyType,   // no need in Android
#endif
            RoleId = roleId,
            OrderNo = orderNo,
            OrderSign = orderSign,
            DeveloperPayload = ""
        };

        pay.PlatformType = PlatformType.Bc;

        Q1CallbackBasicProto<Com.Q1.Sdk.Unity.Result> pay_callback = new Q1CallbackBasicProto<Com.Q1.Sdk.Unity.Result>()
        {
            commonFunc = (Com.Q1.Sdk.Unity.Result result) =>
            {
                Trace("pay callback");
                Trace(result.Message);
                if (result.Result_ == 0)
                {
                    //更改参数。
                    LuaSdkHelper.Instance.OnSDKPay(0);
                    Debug.Log("receive inter call back PaySucceed:");    
                    ReportAdRevenue(money);
                }
                else
                {
                    long res = result.Result_;
                    // res == 1用户取消支付
                    if (res != 0 && res != -8888)
                    {
                        LuaSdkHelper.Instance.OnSDKPay((int)res);
                    }
                    Debug.Log("receive inter call back PayFail:" + result);
                }
            }
        };
        // 调用支付
        Q1SDKAdapterCommon.Instance.CallApi(Com.Q1.Sdk.Unity.Internal.Data.EventCode.X01000200001, pay, pay_callback);
    }

    public void PaySub(string orderItem,
                       string roleId,
                       string orderNo,
                       string orderSign,
                       string money,
                       string currencyType,
                       string userId,
                       int serverId,
                       string offerId)
    {

    }

    public void IOS_Pay(string productId,
         string roleId,
         string serverId,
         string userId, //or userId
         string money,
         string orderItem,
         string orderNO,
         string currencyType,
         string orderSign
      )
    //  string developerPayload,
    //  string notifyUrl
    {
        Trace("Pay()");

        var log = string.Format(@"
    OrderItem: {0}
    RoleId: {1}
    OrderNo: {2}
    OrderSign: {3}
    Money: {4}
    CurrencyType: {5}
    UserId: {6}
    ServerId: {7}",
            orderItem, roleId, orderNO, orderSign, money, currencyType, userId, serverId);
        Trace("=======================begin");
        Trace(log);
        Trace("=======================end");
        

        Com.Q1.Sdk.Unity.Internal.Data.ReqPayData pay = new Com.Q1.Sdk.Unity.Internal.Data.ReqPayData()
        {
            UserId = userId,
            Money = money.ToString(),
            ServerId = serverId.ToString(),
            OrderItem = orderItem,
            
#if UNITY_IPHONE || UNITY_IOS
            ProductId = productId, // no need in Android 
            ProductName = productId, // no need in Android
            CurrencyType = currencyType,   // no need in Android
            PayType = PayType.SkuTypeInapp,
#endif
            RoleId = roleId,
            OrderNo = orderNO,
            OrderSign = orderSign,
            DeveloperPayload = ""
        };

        pay.PlatformType = PlatformType.Apple;

        Q1CallbackBasicProto<Com.Q1.Sdk.Unity.Result> pay_callback = new Q1CallbackBasicProto<Com.Q1.Sdk.Unity.Result>()
        {
            commonFunc = (Com.Q1.Sdk.Unity.Result result) =>
            {
                Trace("pay callback");
                Trace(result.Message);
                if (result.Result_ == 0)
                {
                    //更改参数。
                    LuaSdkHelper.Instance.OnSDKPay(0);
                    Debug.Log("receive inter call back PaySucceed:");    
                }
                else
                {
                    long res = result.Result_;
                    // res == 1用户取消支付
                    if (res != 0 && res != -8888)
                    {
                        LuaSdkHelper.Instance.OnSDKPay((int)res);
                    }
                    Debug.Log("receive inter call back PayFail:" + result);
                }
            }
        };
        // 调用支付
        Q1SDKAdapterCommon.Instance.CallApi(Com.Q1.Sdk.Unity.Internal.Data.EventCode.X01000200001, pay, pay_callback);

    }

        public void IOS_SubPay(string productId,
         string actorId,
         string serverId,
         string userId, //or userId
         string payNum,
         string orderItem,
         string orderNO,
         string currencyType,
         string orderSign
      )
    //  string developerPayload,
    //  string notifyUrl
    {
        Debug.Log("支付参数：productId" + productId);
        Debug.Log("支付参数：actorId" + actorId);

    }

    //游戏开始更新资源时上报，SDK初始化之后调用，示例：
    public void trackUpdateBegin()
    {
        var reqReportData = new ReqReportData();
        FillReqReportData(reqReportData);
        reqReportData.Action = "updateBegin";
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000300029, reqReportData);
    }
    //游戏更新资源完成后上报，示例：
    public void trackUpdateEnd()
    {
        var reqReportData = new ReqReportData();
        FillReqReportData(reqReportData);
        reqReportData.Action = "updateEnd";
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000300029, reqReportData);
    }

    // 服务器session校验成功时上报
    public void trackUserLogin(int serverId, string userId, string roleId, string roleName, int roleLevel)
    {
        var reqReportData = new ReqReportData();
        FillReqReportData(reqReportData);
        reqReportData.Action = "userLogin";
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000300029, reqReportData);
    }
    // 服务器session校验失败时上报
    public void trackUserLoginError(int serverId, string userId, string roleId, string roleName, int roleLevel, string msg)
    {
        var reqReportData = new ReqReportData();
        FillReqReportData(reqReportData);
        reqReportData.Action = "userLoginError";
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000300029, reqReportData);
    }
    
    public void trackRoleLogin(int serverId,
                                  string userId,
                                  string roleId,
                                  string roleName,
                                  int roleLevel)
    {
        pServerId = serverId;
        pGameUserId = userId;
        pRoleId = roleId;
        pRoleName = roleName;
        pRoleLevel = roleLevel;
        
        var reqReportData = new ReqReportData();
        FillReqReportData(reqReportData);
        reqReportData.Action = "login";
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000300029, reqReportData);
        
        ShowAge(false);
    }

    public void trackCreateRole(int serverId,
                          string userId,
                          string roleId,
                          string roleName,
                          int roleLevel)
    {
        pServerId = serverId;
        pGameUserId = userId;
        pRoleId = roleId;
        pRoleName = roleName;
        pRoleLevel = roleLevel;
        
        var reqReportData = new ReqReportData();
        FillReqReportData(reqReportData);
        reqReportData.Action = "create";
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000300029, reqReportData);
    }

    public void trackLevelUp(int serverId,
                                string userId,
                                string roleId,
                                string roleName,
                                int roleLevel)
    {
        pServerId = serverId;
        pGameUserId = userId;
        pRoleId = roleId;
        pRoleName = roleName;
        pRoleLevel = roleLevel;
        
        var reqReportData = new ReqReportData();
        FillReqReportData(reqReportData);
        reqReportData.Action = "upgrade";
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000300029, reqReportData);
    }

    //3.6 创建角色
    //3.5 选择服务器

    //         游戏想切换至其他 Google 账号，调用如下方式：
    // Q1SdkHelper.googleLogin();
    // 切换 Facebook 登录：
    // Q1SdkHelper.facebookLogin();
    // 切换 Huawei 登录：
    // Q1SdkHelper.hwLogin();
    // 切换 Amazon 登录：
    // Q1SdkHelper.amazonLogin();

    public void GoogleLogin()
    {
        Debug.Log("Q1SdkMgr Login");

    }
    public void FacebookLogin()
    {

    }
    public void AppleToLogin()
    {

    }
    public void TwitterToLogin()
    {

    }


    public string GetUuid()
    {
// #if UNITY_ANDROID && !UNITY_EDITOR
//           return q1SdkHelp.CallStatic<string>("Q1GetUuid");
// #endif
// #if UNITY_IOS && !UNITY_EDITOR
//
// #endif
         return SystemInfo.deviceUniqueIdentifier;
    }


    public void QueryProductsAsync()
    {
// #if UNITY_ANDROID && !UNITY_EDITOR
//           q1SdkHelp.CallStatic("queryProductsAsync");
// #endif
    }
    
    public void AIHelpQ1_updateUserInfo(string userId,string roleId)
    {

    }

    public void AIHelpQ1_showWithApiConfig(string entranceId,string welcomeMessage)
    {

    }

    public void Q1_OpenH5Url(string url, bool closeable, int screenType, int windowType, bool isFullScreen)
    {
        ReqH5Data reqH5Data = new()
        {
            Url = url,
            Closeable = closeable,        // 是否显示关闭按钮 false:显示 true:隐藏
            ScreenType = screenType,      // 0:横屏，1:竖屏，2:自适应游戏页面
            WindowType = windowType,      // 窗口类型，0:打开Fragment，1:打开Activity(仅Android）
            IsFullScreen = isFullScreen,  // 是否全屏显示（隐藏导航栏及状态栏）
        };
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000900002, reqH5Data);
    }

    public void FacebookShareUrl(string url)
    {

    }

    private static void Trace(string str)
    {
        Debug.LogError("[TRACE] " + str);
    }
    
    private static  void TraceCallBack(string str)
    {
        Debug.LogError("[TRACE][IQ1InitCallback] " + str);
    }

    private void FillReqReportData(ReqReportData reqReportData)
    {
        reqReportData.RoleId = pRoleId ?? string.Empty;
        reqReportData.RoleName = pRoleName ?? string.Empty;
        reqReportData.RoleLevel = pRoleLevel == 0 ? 1 : pRoleLevel;
        reqReportData.VipLevel = pVipLevel;
        reqReportData.GameUserId = pGameUserId ?? string.Empty;
        reqReportData.ServerId = pServerId;
        reqReportData.ServerName = pServerName ?? "Default";
        reqReportData.TotalRevenue = pTotalRevenue;
        reqReportData.RoleCreateTime = pRoleCreateTime == 0 ? (long)Time.time : pRoleCreateTime;
        reqReportData.RoleLevelTime = pRoleLevelTime == 0 ? (long)Time.time : pRoleLevelTime;

        var log = string.Format(@"
        RoleId: {0}
        RoleName: {1}
        RoleLevel: {2}
        VipLevel: {3}
        GameUserId: {4}
        ServerId: {5}
        ServerName: {6}
        TotalRevenue: {7}
        RoleCreateTime: {8}
        RoleLevelTime: {9}",
            reqReportData.RoleId, 
            reqReportData.RoleName, 
            reqReportData.RoleLevel, 
            reqReportData.VipLevel, 
            reqReportData.GameUserId, 
            reqReportData.ServerId, 
            reqReportData.ServerName, 
            reqReportData.TotalRevenue,
            reqReportData.RoleCreateTime, 
            reqReportData.RoleLevelTime
            );
        
        // Trace("FillReqReportData: " + log);
    }

    /// <summary>
    /// 展示隐私协议链接和双清单
    /// </summary>
    /// <param name="type">1：隐私协议；2：显示用户协议；3：儿童协议；4：个人信息清单；5：第三方共享个人信息清单 默认：1</param>
    public void ShowPrivacyProtocol(int type)
    {
        Trace("ShowPrivacyProtocol:" + type);
        
        ProtocolType protocolType = (ProtocolType)type;
        ReqShowProtocolData reqShowProtocolData = new ReqShowProtocolData();
        reqShowProtocolData.Type = protocolType;
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000100016, reqShowProtocolData);
    }

    /// <summary>
    /// 展示SDK适龄图片
    /// </summary>
    /// <param name="show"></param>
    public void ShowAge(bool show)
    {
        ReqShowRegulatoryData data = new ReqShowRegulatoryData();
        data.Type = show ? RegulatoryType.ShowSdkAgeView : RegulatoryType.HideSdkAgeView;
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000100015, data);
    }
    
    /// <summary>
    /// 推广投放广告 收益上报
    /// </summary>
    /// <param name="money"></param>
    private void ReportAdRevenue(string money)
    {
        ReqPayData para = new ReqPayData();
        para.Money = money;// 支付金额（单位元）
        Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01010400001, para);
    }
    
    #region 协议回调
    public void OnLoginResult(RestLoginData logindata)
    {
        if (logindata == null || logindata.UserInfo == null)
        {
            Trace("OnLoginResult(RestLoginData logindata): logindata is null");
            return;
        }
        
        m_UserData = logindata.UserInfo;
        
        Debug.Log("receive inter call back LoginSucceed:" + logindata.UserInfo.UserId);
        string userId = logindata.UserInfo.UserId;
        string session = logindata.UserInfo.Session;
        
        if (string.IsNullOrEmpty(session))
        {
            Debug.Log("登录成功回来session是空" + session);
        }
        
        Debug.Log("receive inter call back LoginSucceed:" + session);
        Debug.Log("receive inter call back LoginSucceed  userId:" + userId);
        
        ThingkingAnalyticsMgr.Instance.TrackEvent(ThinkingKey.sdk_login_success);
        ThingkingAnalyticsMgr.Instance.TrackEvent("sdk_login_session" , "session" , session);
        
        StartGame.Instance.SetUserId(userId);
        StartGame.Instance.SetSDKToken(session);
        byte[] sou = Encoding.UTF8.GetBytes(session);
        session = Convert.ToBase64String(sou);
        SDKLoginModule.Instance.SetBindType(0);
        SDKLoginModule.Instance.RequestLoginSdkBack(0, 0, session);
        
    }

    public void OnLoginResult(Result logindata)
    {
        Trace("OnLoginResult(Result logindata):"+logindata.Message);
    }

    public void OnPrivacyAgreedResult(RestPrivacyAgreedData pridata)
    {
        Trace("OnPrivacyAgreedResult(RestPrivacyAgreedData pridata):"+pridata.IsAgree);
#if UNITY_IPHONE
        if(pridata.IsAgree){
            m_IsPrivacyAgreed = pridata.IsAgree;
            TryLogin();
        }
#endif
    }

    public void OnPrivacyAgreedResult(Result pridata)
    {
        Trace("OnPrivacyAgreedResult(Result pridata):" + pridata.Message);
    }

    public void OnInit(Result result)
    {
        Trace("OnInit(Result result):" + result.Message);
        if (result.Result_ == 0)
        {
            ShowAge(true);
        }
    }

    public void OnGetConfigResult(RestGetConfigData config)
    {
        Trace("OnGetConfigResult(RestGetConfigData config)" + config.ToString());
    }

    public void OnGetConfigResult(Result config)
    {
        Trace("OnGetConfigResult(Result config)" + config.Message);
    }

    public void OnAuthResult(RestUserStateData userstate)
    {
        Trace("OnAuthResult(RestUserStateData userstate)" + userstate.IsIdAuth);
    }

    public void OnAuthResult(Result userstate)
    {
        Trace("OnAuthResult(Result userstate)" + userstate.Message);
    }

    public void OnUpdateTokenResult(RestUpdateTokenData tokendata)
    {
        Trace("OnUpdateTokenResult(RestUpdateTokenData tokendata)"+tokendata.Token);
    }

    public void OnUpdateTokenResult(Result tokendata)
    {
        Trace("OnUpdateTokenResult(Result tokendata)"+tokendata.Message);
    }

    public void OnSwitchAccountResult(Result tokendata)
    {
        Trace("OnSwitchAccountResult(Result tokendata)" + tokendata.Message);
    }

    public void OnChangePasswordLoginResult(RestLoginData logindata)
    {
        Trace("OnChangePasswordLoginResult(RestLoginData logindata)" + logindata.UserInfo.UserId);
    }

    public void OnChangePasswordLoginResult(Result logindata)
    {
        Trace("OnChangePasswordLoginResult(Result logindata)" + logindata.Message);
    }

    public void OnChangeEnvironmentResult(Result result)
    {
        Trace("OnChangeEnvironmentResult(Result result)" + result.Message);
    }

    public void OnBindingResult(Result result)
    {
        Trace("OnBindingResult(Result result)" + result.Message);
    }

    public void onResult(RestU8ResultData result)
    {
        Trace("onResult(RestU8ResultData result)" + result.Msg);
    }
    #endregion

    public string GetUserName()
    {
        return m_UserData != null ? m_UserData.UserName : string.Empty;
    }

    public string GetPID()
    {
        RestSDKPropertiesData res_data = (RestSDKPropertiesData)Q1SDKAdapterCommon.Instance.CallApi(EventCode.X01000900021, RestSDKPropertiesData.Descriptor);
        return res_data.Pid;
    }
}
