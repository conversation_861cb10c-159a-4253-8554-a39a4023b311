using DeletegateCall;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.RegularExpressions;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.UI;

/// <summary>
/// 文本控件,支持超链接
/// </summary>
public class HyperlinkText : Text, IPointerClickHandler
{
    /// <summary>
    /// 超链接信息类
    /// </summary>
    private class HyperlinkInfo
    {
        public int startIndex;
        public int endIndex;
        public string refValue;
        public string innerValue;
        public List<Rect> boxList = new();
    }

    /// <summary>
    /// 超链接信息列表
    /// </summary>
    readonly List<HyperlinkInfo> hyperlinkInfoList = new();

    /// <summary>
    /// 文本构造器
    /// </summary>
    protected static readonly StringBuilder s_TextBuilder = new StringBuilder();

    [Serializable]
    public class HrefClickEvent : UnityEvent<string> { }

    [SerializeField]
    private HrefClickEvent m_OnHrefClick = new HrefClickEvent();

    public DeleteCell<string> OnClick = null;

    /// <summary>
    /// 超链接点击事件
    /// </summary>
    public HrefClickEvent onHrefClick
    {
        get { return m_OnHrefClick; }
        set { m_OnHrefClick = value; }
    }

    /// <summary>
    /// 超链接正则
    /// </summary>
    readonly Regex hrefRegex = new(@"<a href=([^>\n\s]+)>(.*?)(</a>)", RegexOptions.Singleline);
    readonly Regex colorRegex = new(@"<color=([^>\n\s]+)>(.*?)(</color>)", RegexOptions.Singleline);
    readonly Regex colorPreRegex = new(@"<color=([^>\n\s]+)>", RegexOptions.Singleline);
    readonly Regex colorEndRegex = new(@"</color>", RegexOptions.Singleline);

    private HyperlinkText mHyperlinkText;

    public string GetHyperlinkInfo
    {
        get { return text; }
    }

    public bool IsShowUnderline { get; set; }
    Color hyperlineColor = Color.black;
    public float hyperlineOffsetX = 2f;  // 下划线水平偏移量
    public float hyperlineOffsetY = 6f;  // 下划线垂直偏移量

    protected override void Awake()
    {
        base.Awake();
        mHyperlinkText = GetComponent<HyperlinkText>();
    }
    protected override void OnEnable()
    {
        base.OnEnable();
        mHyperlinkText.onHrefClick.AddListener(OnHyperlinkTextInfo);
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        mHyperlinkText.onHrefClick.RemoveListener(OnHyperlinkTextInfo);
    }


    public override void SetVerticesDirty()
    {
        base.SetVerticesDirty();
#if UNITY_EDITOR
        if (UnityEditor.PrefabUtility.GetPrefabType(this) == UnityEditor.PrefabType.Prefab)
        {
            return;
        }
#endif
    }

    protected override void OnPopulateMesh(VertexHelper toFill)
    {
        base.OnPopulateMesh(toFill);
        InitHyperlinkInfo();
        InitHyperlinkBox(toFill);
        if (IsShowUnderline)
        {
            DrawUnderLine(toFill);
        }
    }

    /// <summary>
    /// 点击事件检测是否点击到超链接文本
    /// </summary>
    /// <param name="eventData"></param>
    public void OnPointerClick(PointerEventData eventData)
    {
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            rectTransform, eventData.position, eventData.pressEventCamera, out Vector2 localPoint);

        foreach (HyperlinkInfo hyperlinkInfo in hyperlinkInfoList)
        {
            var boxeList = hyperlinkInfo.boxList;
            for (var i = 0; i < boxeList.Count; ++i)
            {
                if (boxeList[i].Contains(localPoint))
                {
                    m_OnHrefClick?.Invoke(hyperlinkInfo.refValue);
                    return;
                }
            }
        }
    }

    /// <summary>
    /// 当前点击超链接回调
    /// </summary>
    /// <param name="info">回调信息</param>
    private void OnHyperlinkTextInfo(string info)
    {
        if (OnClick != null)
        {
            OnClick.Invoke(info);
        }
        Debug.Log("超链接信息：" + info);
    }

    /// <summary>
    /// 初始化超链接信息
    /// </summary>
    void InitHyperlinkInfo()
    {
        GetOutputText(text);
    }

    /// <summary>
    /// 获取超链接文本内容
    /// </summary>
    /// <param name="outputText">文本</param>
    /// <returns>去掉富文本标签后的文本</returns>
    string GetOutputText(string outputText)
    {
        StringBuilder stringBuilder = new();

        hyperlinkInfoList.Clear();

        int strIndex = 0;

        foreach (Match match in hrefRegex.Matches(outputText))
        {
            string appendStr = outputText[strIndex..match.Index];

            // 空格和回车没有顶点渲染，所以要去掉
            appendStr = appendStr.Replace(" ", "");
            appendStr = appendStr.Replace("\n", "");

            appendStr = RichStringFilter(appendStr);

            stringBuilder.Append(appendStr);

            int startIndex = stringBuilder.Length;

            // 第一个是 url，第二个是链接文本，跳转用 url，计算 index 用文本
            Group urlGroup = match.Groups[1];
            Group titleGroup = match.Groups[2];

            // 如果有 Color 语法嵌套，则还要继续扒，直到把最终文本扒出来
            Match colorMatch = colorRegex.Match(titleGroup.Value);

            if (colorMatch.Groups.Count > 3)
            {
                titleGroup = colorMatch.Groups[2];
                Group colorGroup = colorMatch.Groups[1];
                string colorStr = colorGroup.Value[1..^1];
                ColorUtility.TryParseHtmlString(colorStr, out Color nowColor);
                hyperlineColor = nowColor;
            }

            var inner = titleGroup.Value;
            inner = inner.Replace(" ", "");
            inner = inner.Replace("\n", "");

            stringBuilder.Append(inner);

            HyperlinkInfo hyperlinkInfo = new()
            {
                startIndex = startIndex,
                endIndex = startIndex + inner.Length,
                refValue = urlGroup.Value[1..^1],
                innerValue = titleGroup.Value,
            };

            strIndex = match.Index + match.Length;
            hyperlinkInfoList.Add(hyperlinkInfo);
        }

        stringBuilder.Append(outputText[strIndex..]);
        return stringBuilder.ToString();
    }

    /// <summary>
    /// 富文本过滤器，去掉颜色标签
    /// </summary>
    /// <param name="outputText">文本</param>
    /// <returns>去掉富文本标签后的文本</returns>
    private string RichStringFilter(string outputText)
    {
        outputText = colorPreRegex.Replace(outputText, "");
        outputText = colorEndRegex.Replace(outputText, "");

        return outputText;
    }

    /// <summary>
    /// 初始化超链接包围框
    /// </summary>
    /// <param name="toFill">填充区域</param>
    void InitHyperlinkBox(VertexHelper toFill)
    {
        UIVertex vert = new();

        // 处理超链接包围框
        foreach (var hrefInfo in hyperlinkInfoList)
        {
            hrefInfo.boxList.Clear();

            // 一个字符是四个顶点，所以 Index 要乘以 4
            int startVertex = hrefInfo.startIndex * 4;
            int endVertex = hrefInfo.endIndex * 4;

            if (startVertex >= toFill.currentVertCount)
            {
                continue;
            }

            // 将超链接里面的文本顶点索引坐标加入到包围框
            toFill.PopulateUIVertex(ref vert, startVertex);

            var pos = vert.position;
            var bounds = new Bounds(pos, Vector3.zero);

            for (int i = startVertex; i < endVertex; i++)
            {
                if (i >= toFill.currentVertCount)
                {
                    break;
                }

                toFill.PopulateUIVertex(ref vert, i);

                pos = vert.position;

                bool needEncapsulate = true;

                if ((i - startVertex) % 4 == 0)
                {
                    if (i < 4) continue;

                    UIVertex lastV = new();
                    toFill.PopulateUIVertex(ref lastV, i - 4);
                    var lastPos = lastV.position;

                    if (pos.x < lastPos.x && pos.y < lastPos.y) // 换行重新添加包围框
                    {
                        hrefInfo.boxList.Add(new Rect(bounds.min, bounds.size));
                        bounds = new Bounds(pos, Vector3.zero);
                        needEncapsulate = false;
                    }
                }

                if (needEncapsulate)
                {
                    bounds.Encapsulate(pos); // 扩展包围框
                }
            }

            hrefInfo.boxList.Add(new Rect(bounds.min, bounds.size));
        }
    }

    /// <summary>
    /// 绘制下划线
    /// </summary>
    /// <param name="vh">填充区域</param>
    void DrawUnderLine(VertexHelper vh)
    {
        for (int i = 0; i < hyperlinkInfoList.Count; i++)
        {
            HyperlinkInfo info = hyperlinkInfoList[i];
            for (int j = 0; j < info.boxList.Count; j++)
            {
                Rect rect = info.boxList[j];
                float height = rect.height;
                
                var pos1 = new Vector3(rect.min.x, rect.min.y, 0); // 左下
                var pos2 = new Vector3(rect.max.x, rect.max.y, 0) - new Vector3(0, height, 0); // 右下

                MeshUnderLine(vh, pos1, pos2);
            }
        }
    }

    /// <summary>
    /// 绘制下划线网格
    /// </summary>
    /// <param name="vh">填充区域</param>
    /// <param name="startPos">起始点</param>
    /// <param name="endPos">结束点</param>
    void MeshUnderLine(VertexHelper vh, Vector2 startPos, Vector2 endPos)
    {            
        Vector2 extents = rectTransform.rect.size;
        var setting = GetGenerationSettings(extents);

        TextGenerator underlineText = new();
        underlineText.Populate("￣", setting);

        IList<UIVertex> lineVer = underlineText.verts; // "￣" 的顶点数组

        Vector3[] pos = new Vector3[4];
        pos[0] = startPos + new Vector2(-hyperlineOffsetX, 0);
        pos[3] = startPos + new Vector2(-hyperlineOffsetX, -hyperlineOffsetY);
        pos[2] = endPos + new Vector2(hyperlineOffsetX, -hyperlineOffsetY);
        pos[1] = endPos + new Vector2(hyperlineOffsetX, 0);

        if (lineVer.Count == 4)
        {
            UIVertex[] tempVerts = new UIVertex[4];
            for (int i = 0; i < 4; i++)
            {
                tempVerts[i] = lineVer[i];
                tempVerts[i].color = hyperlineColor;
                tempVerts[i].position = pos[i];
                tempVerts[i].uv0 = lineVer[i].uv0;
                tempVerts[i].uv1 = lineVer[i].uv1;
                tempVerts[i].uv2 = lineVer[i].uv2;
                tempVerts[i].uv3 = lineVer[i].uv3;
            }
            vh.AddUIVertexQuad(tempVerts);
        }
    }
}
