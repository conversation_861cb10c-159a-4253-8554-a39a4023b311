﻿#if USE_UNI_LUA
using LuaAPI = UniLua.Lua;
using RealStatePtr = UniLua.ILuaState;
using LuaCSFunction = UniLua.CSharpFunctionDelegate;
#else
using LuaAPI = XLua.LuaDLL.Lua;
using RealStatePtr = System.IntPtr;
using LuaCSFunction = XLua.LuaDLL.lua_CSFunction;
#endif

using XLua;
using System.Collections.Generic;


namespace XLua.CSObjectWrap
{
    using Utils = XLua.Utils;
    public class LuaSdkHelperWrap 
    {
        public static void __Register(RealStatePtr L)
        {
			ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			System.Type type = typeof(LuaSdkHelper);
			Utils.BeginObjectRegister(type, L, translator, 0, 41, 0, 0);
			
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "LoadPB", _m_LoadPB);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "OnSDKPay", _m_OnSDKPay);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "SDKPay", _m_SDKPay);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "Q1SDKPay", _m_Q1SDKPay);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "Q1SDKPaySub", _m_Q1SDKPaySub);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "SDKInit", _m_SDKInit);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "ADInit", _m_ADInit);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "ShowAdsConsentForm", _m_ShowAdsConsentForm);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "CanShowAdsConsentForm", _m_CanShowAdsConsentForm);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "ADMovieState", _m_ADMovieState);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "ADMoviePlay", _m_ADMoviePlay);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "ADMovieBackErr", _m_ADMovieBackErr);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "ADMovieBackClick", _m_ADMovieBackClick);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "ADMovieBackWin", _m_ADMovieBackWin);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetGoogleAAID", _m_GetGoogleAAID);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "AddRepeatPushTimer", _m_AddRepeatPushTimer);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "InputGuideInfo", _m_InputGuideInfo);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "AddPushNotices", _m_AddPushNotices);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "RemovePushNotices", _m_RemovePushNotices);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "RemoveAllPushNotices", _m_RemoveAllPushNotices);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "Copy", _m_Copy);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "StartDownloadSilent", _m_StartDownloadSilent);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "StartDownloadSilentByMapAndUI", _m_StartDownloadSilentByMapAndUI);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "StartDownloadSilentByMap", _m_StartDownloadSilentByMap);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "CheckHasFile", _m_CheckHasFile);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetParticleStartSizeConstant", _m_GetParticleStartSizeConstant);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "SetParticleStartSizeMinMaxCurve", _m_SetParticleStartSizeMinMaxCurve);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "NewTgMesh", _m_NewTgMesh);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "SetPriceJson", _m_SetPriceJson);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "SetPriceJsonAmount", _m_SetPriceJsonAmount);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetStringByType", _m_GetStringByType);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "Q1HelperCount", _m_Q1HelperCount);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "AIHelpQ1_updateUserInfo", _m_AIHelpQ1_updateUserInfo);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "AIHelpQ1_showWithApiConfig", _m_AIHelpQ1_showWithApiConfig);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "OpenH5Url", _m_OpenH5Url);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "FacebookShareUrl", _m_FacebookShareUrl);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetRoleId", _m_GetRoleId);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetGameUserId", _m_GetGameUserId);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetServerId", _m_GetServerId);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetUserName", _m_GetUserName);
			Utils.RegisterFunc(L, Utils.METHOD_IDX, "GetPID", _m_GetPID);
			
			
			
			
			
			Utils.EndObjectRegister(type, L, translator, null, null,
			    null, null, null);

		    Utils.BeginClassRegister(type, L, __CreateInstance, 1, 1, 0);
			
			
            
			Utils.RegisterFunc(L, Utils.CLS_GETTER_IDX, "Instance", _g_get_Instance);
            
			
			
			Utils.EndClassRegister(type, L, translator);
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int __CreateInstance(RealStatePtr L)
        {
            
			try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
				if(LuaAPI.lua_gettop(L) == 1)
				{
					
					LuaSdkHelper gen_ret = new LuaSdkHelper();
					translator.Push(L, gen_ret);
                    
					return 1;
				}
				
			}
			catch(System.Exception gen_e) {
				return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
			}
            return LuaAPI.luaL_error(L, "invalid arguments to LuaSdkHelper constructor!");
            
        }
        
		
        
		
        
        
        
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_LoadPB(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                        byte[] gen_ret = gen_to_be_invoked.LoadPB(  );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_OnSDKPay(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    int _code = LuaAPI.xlua_tointeger(L, 2);
                    
                    gen_to_be_invoked.OnSDKPay( _code );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SDKPay(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _productId = LuaAPI.lua_tostring(L, 2);
                    string _orderId = LuaAPI.lua_tostring(L, 3);
                    
                        int gen_ret = gen_to_be_invoked.SDKPay( _productId, _orderId );

                        LuaAPI.xlua_pushinteger(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_Q1SDKPay(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _orderItem = LuaAPI.lua_tostring(L, 2);
                    string _roleId = LuaAPI.lua_tostring(L, 3);
                    string _orderNo = LuaAPI.lua_tostring(L, 4);
                    string _orderSign = LuaAPI.lua_tostring(L, 5);
                    string _currencyType = LuaAPI.lua_tostring(L, 6);
                    string _userId = LuaAPI.lua_tostring(L, 7);
                    int _serverId = LuaAPI.xlua_tointeger(L, 8);
                    string _money = LuaAPI.lua_tostring(L, 9);
                    string _payCode = LuaAPI.lua_tostring(L, 10);
                    
                        int gen_ret = gen_to_be_invoked.Q1SDKPay( _orderItem, _roleId, _orderNo, _orderSign, _currencyType, _userId, _serverId, _money, _payCode );

                        LuaAPI.xlua_pushinteger(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_Q1SDKPaySub(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _orderItem = LuaAPI.lua_tostring(L, 2);
                    string _roleId = LuaAPI.lua_tostring(L, 3);
                    string _orderNo = LuaAPI.lua_tostring(L, 4);
                    string _orderSign = LuaAPI.lua_tostring(L, 5);
                    string _currencyType = LuaAPI.lua_tostring(L, 6);
                    string _userId = LuaAPI.lua_tostring(L, 7);
                    int _serverId = LuaAPI.xlua_tointeger(L, 8);
                    string _money = LuaAPI.lua_tostring(L, 9);
                    string _offerId = LuaAPI.lua_tostring(L, 10);
                    string _payCode = LuaAPI.lua_tostring(L, 11);
                    
                        int gen_ret = gen_to_be_invoked.Q1SDKPaySub( _orderItem, _roleId, _orderNo, _orderSign, _currencyType, _userId, _serverId, _money, _offerId, _payCode );

                        LuaAPI.xlua_pushinteger(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SDKInit(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _payInf = LuaAPI.lua_tostring(L, 2);
                    
                    gen_to_be_invoked.SDKInit( _payInf );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ADInit(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _appkey = LuaAPI.lua_tostring(L, 2);
                    bool _isDebug = LuaAPI.lua_toboolean(L, 3);
                    
                    gen_to_be_invoked.ADInit( _appkey, _isDebug );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ShowAdsConsentForm(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                    gen_to_be_invoked.ShowAdsConsentForm(  );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_CanShowAdsConsentForm(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                        bool gen_ret = gen_to_be_invoked.CanShowAdsConsentForm(  );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ADMovieState(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    int _adId = LuaAPI.xlua_tointeger(L, 2);
                    string _adUnityType = LuaAPI.lua_tostring(L, 3);
                    
                        bool gen_ret = gen_to_be_invoked.ADMovieState( _adId, _adUnityType );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ADMoviePlay(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    int _adId = LuaAPI.xlua_tointeger(L, 2);
                    string _adUnityType = LuaAPI.lua_tostring(L, 3);
                    
                        int gen_ret = gen_to_be_invoked.ADMoviePlay( _adId, _adUnityType );

                        LuaAPI.xlua_pushinteger(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ADMovieBackErr(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    int _adId = LuaAPI.xlua_tointeger(L, 2);
                    int _code = LuaAPI.xlua_tointeger(L, 3);
                    string _des = LuaAPI.lua_tostring(L, 4);
                    
                    gen_to_be_invoked.ADMovieBackErr( _adId, _code, _des );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ADMovieBackClick(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    int _adId = LuaAPI.xlua_tointeger(L, 2);
                    
                    gen_to_be_invoked.ADMovieBackClick( _adId );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_ADMovieBackWin(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    int _adId = LuaAPI.xlua_tointeger(L, 2);
                    
                    gen_to_be_invoked.ADMovieBackWin( _adId );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetGoogleAAID(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                        string gen_ret = gen_to_be_invoked.GetGoogleAAID(  );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_AddRepeatPushTimer(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _param = LuaAPI.lua_tostring(L, 2);
                    
                    gen_to_be_invoked.AddRepeatPushTimer( _param );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_InputGuideInfo(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _uid = LuaAPI.lua_tostring(L, 2);
                    
                    gen_to_be_invoked.InputGuideInfo( _uid );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_AddPushNotices(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _param = LuaAPI.lua_tostring(L, 2);
                    
                    gen_to_be_invoked.AddPushNotices( _param );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_RemovePushNotices(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _param = LuaAPI.lua_tostring(L, 2);
                    
                    gen_to_be_invoked.RemovePushNotices( _param );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_RemoveAllPushNotices(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                    gen_to_be_invoked.RemoveAllPushNotices(  );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_Copy(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _str = LuaAPI.lua_tostring(L, 2);
                    
                    gen_to_be_invoked.Copy( _str );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_StartDownloadSilent(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    System.Action<bool> _backFun = translator.GetDelegate<System.Action<bool>>(L, 2);
                    
                    gen_to_be_invoked.StartDownloadSilent( _backFun );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_StartDownloadSilentByMapAndUI(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    System.Collections.Generic.List<string> _keys = (System.Collections.Generic.List<string>)translator.GetObject(L, 2, typeof(System.Collections.Generic.List<string>));
                    System.Action<bool> _backFun = translator.GetDelegate<System.Action<bool>>(L, 3);
                    bool _isShow = LuaAPI.lua_toboolean(L, 4);
                    
                    gen_to_be_invoked.StartDownloadSilentByMapAndUI( _keys, _backFun, _isShow );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_StartDownloadSilentByMap(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    System.Collections.Generic.List<string> _keys = (System.Collections.Generic.List<string>)translator.GetObject(L, 2, typeof(System.Collections.Generic.List<string>));
                    System.Action<bool> _backFun = translator.GetDelegate<System.Action<bool>>(L, 3);
                    
                    gen_to_be_invoked.StartDownloadSilentByMap( _keys, _backFun );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_CheckHasFile(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _FileName = LuaAPI.lua_tostring(L, 2);
                    
                        bool gen_ret = gen_to_be_invoked.CheckHasFile( _FileName );

                        LuaAPI.lua_pushboolean(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetParticleStartSizeConstant(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    UnityEngine.ParticleSystem _particle = (UnityEngine.ParticleSystem)translator.GetObject(L, 2, typeof(UnityEngine.ParticleSystem));
                    
                        float gen_ret = gen_to_be_invoked.GetParticleStartSizeConstant( _particle );

                        LuaAPI.lua_pushnumber(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetParticleStartSizeMinMaxCurve(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    UnityEngine.ParticleSystem _particle = (UnityEngine.ParticleSystem)translator.GetObject(L, 2, typeof(UnityEngine.ParticleSystem));
                    float _x = (float)LuaAPI.lua_tonumber(L, 3);
                    float _y = (float)LuaAPI.lua_tonumber(L, 4);
                    
                    gen_to_be_invoked.SetParticleStartSizeMinMaxCurve( _particle, _x, _y );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_NewTgMesh(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                        UnityEngine.Mesh gen_ret = gen_to_be_invoked.NewTgMesh(  );

                        translator.Push(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetPriceJson(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _str = LuaAPI.lua_tostring(L, 2);
                    
                    gen_to_be_invoked.SetPriceJson( _str );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_SetPriceJsonAmount(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _priceAmount = LuaAPI.lua_tostring(L, 2);
                    
                    gen_to_be_invoked.SetPriceJsonAmount( _priceAmount );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetStringByType(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
			    int gen_param_count = LuaAPI.lua_gettop(L);
            
                if(gen_param_count == 2&& LuaTypes.LUA_TNUMBER == LuaAPI.lua_type(L, 2)) 
                {
                    int _type = LuaAPI.xlua_tointeger(L, 2);
                    
                        string gen_ret = gen_to_be_invoked.GetStringByType( _type );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                if(gen_param_count == 1) 
                {
                    
                        string gen_ret = gen_to_be_invoked.GetStringByType(  );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
            return LuaAPI.luaL_error(L, "invalid arguments to LuaSdkHelper.GetStringByType!");
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_Q1HelperCount(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    int _count = LuaAPI.xlua_tointeger(L, 2);
                    
                    gen_to_be_invoked.Q1HelperCount( _count );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_AIHelpQ1_updateUserInfo(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                    gen_to_be_invoked.AIHelpQ1_updateUserInfo(  );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_AIHelpQ1_showWithApiConfig(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _entranceId = LuaAPI.lua_tostring(L, 2);
                    string _welcomeMessage = LuaAPI.lua_tostring(L, 3);
                    
                    gen_to_be_invoked.AIHelpQ1_showWithApiConfig( _entranceId, _welcomeMessage );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_OpenH5Url(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _url = LuaAPI.lua_tostring(L, 2);
                    bool _closeable = LuaAPI.lua_toboolean(L, 3);
                    int _screenType = LuaAPI.xlua_tointeger(L, 4);
                    int _windowType = LuaAPI.xlua_tointeger(L, 5);
                    bool _isFullScreen = LuaAPI.lua_toboolean(L, 6);
                    
                    gen_to_be_invoked.OpenH5Url( _url, _closeable, _screenType, _windowType, _isFullScreen );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_FacebookShareUrl(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    string _url = LuaAPI.lua_tostring(L, 2);
                    
                    gen_to_be_invoked.FacebookShareUrl( _url );

                    
                    
                    
                    return 0;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetRoleId(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                        string gen_ret = gen_to_be_invoked.GetRoleId(  );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetGameUserId(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                        string gen_ret = gen_to_be_invoked.GetGameUserId(  );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetServerId(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                        int gen_ret = gen_to_be_invoked.GetServerId(  );

                        LuaAPI.xlua_pushinteger(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetUserName(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                        string gen_ret = gen_to_be_invoked.GetUserName(  );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _m_GetPID(RealStatePtr L)
        {
		    try {
            
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
            
            
                LuaSdkHelper gen_to_be_invoked = (LuaSdkHelper)translator.FastGetCSObj(L, 1);
            
            
                
                {
                    
                        string gen_ret = gen_to_be_invoked.GetPID(  );

                        LuaAPI.lua_pushstring(L, gen_ret);
                    
                    
                    
                    return 1;
                }
                
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            
        }
        
        
        
        
        [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
        static int _g_get_Instance(RealStatePtr L)
        {
		    try {
                ObjectTranslator translator = ObjectTranslatorPool.Instance.Find(L);
			    translator.Push(L, LuaSdkHelper.Instance);
            } catch(System.Exception gen_e) {
                return LuaAPI.luaL_error(L, "c# exception:" + gen_e);
            }
            return 1;
        }
        
        
        
		
		
		
		
    }
}
