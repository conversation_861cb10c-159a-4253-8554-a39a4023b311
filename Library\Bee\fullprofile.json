{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21572, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21572, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21572, "tid": 429, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21572, "tid": 429, "ts": 1756369567320151, "dur": 429, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21572, "tid": 429, "ts": 1756369567322672, "dur": 447, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21572, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21572, "tid": 1, "ts": 1756369545177764, "dur": 19586, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21572, "tid": 1, "ts": 1756369545197354, "dur": 35265, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21572, "tid": 1, "ts": 1756369545232626, "dur": 55995, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21572, "tid": 429, "ts": 1756369567323121, "dur": 5, "ph": "X", "name": "", "args": {}}, {"pid": 21572, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545176513, "dur": 6207, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545182722, "dur": 22132228, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545183354, "dur": 1330, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545184690, "dur": 349, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545185040, "dur": 7391, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545192440, "dur": 228, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545192671, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545192698, "dur": 517, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545193217, "dur": 28983, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545222209, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545222212, "dur": 719, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545222935, "dur": 626, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545223563, "dur": 295, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545223861, "dur": 6, "ph": "X", "name": "ProcessMessages 18877", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545223868, "dur": 30, "ph": "X", "name": "ReadAsync 18877", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545223901, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545223902, "dur": 24, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545223928, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545223929, "dur": 16, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545223947, "dur": 13, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545223962, "dur": 62, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224030, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224058, "dur": 1, "ph": "X", "name": "ProcessMessages 1053", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224061, "dur": 21, "ph": "X", "name": "ReadAsync 1053", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224085, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224087, "dur": 68, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224158, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224185, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224187, "dur": 102, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224292, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224318, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224320, "dur": 25, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224347, "dur": 1, "ph": "X", "name": "ProcessMessages 554", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224349, "dur": 20, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224372, "dur": 15, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224391, "dur": 17, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224409, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224412, "dur": 17, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224432, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224433, "dur": 25, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224460, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224461, "dur": 16, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224479, "dur": 16, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224499, "dur": 44, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224546, "dur": 18, "ph": "X", "name": "ReadAsync 920", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224566, "dur": 16, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224584, "dur": 14, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224600, "dur": 24, "ph": "X", "name": "ReadAsync 69", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224627, "dur": 1, "ph": "X", "name": "ProcessMessages 402", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224629, "dur": 96, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224729, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224757, "dur": 1, "ph": "X", "name": "ProcessMessages 1085", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224759, "dur": 27, "ph": "X", "name": "ReadAsync 1085", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224788, "dur": 16, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224809, "dur": 34, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224847, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224849, "dur": 25, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224876, "dur": 1, "ph": "X", "name": "ProcessMessages 1023", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224878, "dur": 17, "ph": "X", "name": "ReadAsync 1023", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545224898, "dur": 114, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225017, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225047, "dur": 11, "ph": "X", "name": "ReadAsync 968", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225060, "dur": 10, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225072, "dur": 26, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225101, "dur": 11, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225113, "dur": 9, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225124, "dur": 30, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225157, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225169, "dur": 11, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225182, "dur": 15, "ph": "X", "name": "ReadAsync 303", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225199, "dur": 10, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225211, "dur": 15, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225228, "dur": 8, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225239, "dur": 8, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225249, "dur": 10, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225260, "dur": 11, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225273, "dur": 11, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225285, "dur": 10, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225297, "dur": 10, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225309, "dur": 9, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225320, "dur": 11, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225333, "dur": 125, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225460, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225472, "dur": 31, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225506, "dur": 1, "ph": "X", "name": "ProcessMessages 466", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225508, "dur": 36, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225547, "dur": 1, "ph": "X", "name": "ProcessMessages 1066", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225549, "dur": 13, "ph": "X", "name": "ReadAsync 1066", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225564, "dur": 24, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225590, "dur": 14, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225606, "dur": 17, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225624, "dur": 11, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225637, "dur": 10, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225649, "dur": 9, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225660, "dur": 11, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225674, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225688, "dur": 11, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225701, "dur": 12, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225715, "dur": 11, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225727, "dur": 18, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225747, "dur": 11, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225759, "dur": 10, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225771, "dur": 129, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225902, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225916, "dur": 11, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225928, "dur": 12, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225942, "dur": 13, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225957, "dur": 11, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225970, "dur": 10, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225981, "dur": 11, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545225994, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226012, "dur": 10, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226024, "dur": 11, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226037, "dur": 11, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226050, "dur": 11, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226063, "dur": 9, "ph": "X", "name": "ReadAsync 326", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226074, "dur": 12, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226088, "dur": 12, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226101, "dur": 10, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226114, "dur": 17, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226133, "dur": 10, "ph": "X", "name": "ReadAsync 644", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226145, "dur": 10, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226157, "dur": 10, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226169, "dur": 9, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226180, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226210, "dur": 16, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226228, "dur": 12, "ph": "X", "name": "ReadAsync 763", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226242, "dur": 12, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226256, "dur": 12, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226269, "dur": 10, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226281, "dur": 28, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226311, "dur": 13, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226326, "dur": 12, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226340, "dur": 22, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226364, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226377, "dur": 10, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226389, "dur": 9, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226400, "dur": 10, "ph": "X", "name": "ReadAsync 230", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226412, "dur": 11, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226424, "dur": 12, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226439, "dur": 11, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226452, "dur": 16, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226470, "dur": 152, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226623, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226637, "dur": 11, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226649, "dur": 10, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226661, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226685, "dur": 11, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226697, "dur": 10, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226710, "dur": 14, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226726, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226739, "dur": 10, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226751, "dur": 13, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226766, "dur": 12, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226780, "dur": 15, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226795, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226797, "dur": 10, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226810, "dur": 19, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226831, "dur": 12, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226844, "dur": 11, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226857, "dur": 28, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226888, "dur": 16, "ph": "X", "name": "ReadAsync 186", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226907, "dur": 32, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545226940, "dur": 122, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227064, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227101, "dur": 15, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227118, "dur": 13, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227133, "dur": 30, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227164, "dur": 102, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227268, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227283, "dur": 11, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227295, "dur": 10, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227307, "dur": 10, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227318, "dur": 11, "ph": "X", "name": "ReadAsync 113", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227330, "dur": 11, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227343, "dur": 9, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227353, "dur": 9, "ph": "X", "name": "ReadAsync 191", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227364, "dur": 16, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227381, "dur": 12, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227394, "dur": 32, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227428, "dur": 16, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227445, "dur": 9, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227456, "dur": 9, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227475, "dur": 22, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227500, "dur": 15, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227517, "dur": 12, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227531, "dur": 13, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227545, "dur": 17, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227564, "dur": 24, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227589, "dur": 10, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227600, "dur": 9, "ph": "X", "name": "ReadAsync 120", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227611, "dur": 10, "ph": "X", "name": "ReadAsync 209", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227623, "dur": 32, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227656, "dur": 9, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227667, "dur": 22, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227690, "dur": 131, "ph": "X", "name": "ReadAsync 259", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227823, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227838, "dur": 12, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227853, "dur": 14, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227868, "dur": 10, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227880, "dur": 9, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227891, "dur": 9, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227902, "dur": 9, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545227913, "dur": 110, "ph": "X", "name": "ReadAsync 247", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228025, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228037, "dur": 15, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228054, "dur": 10, "ph": "X", "name": "ReadAsync 366", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228066, "dur": 11, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228080, "dur": 15, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228096, "dur": 14, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228112, "dur": 7, "ph": "X", "name": "ReadAsync 223", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228122, "dur": 12, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228135, "dur": 11, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228148, "dur": 10, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228159, "dur": 9, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228169, "dur": 11, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228182, "dur": 12, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228196, "dur": 10, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228207, "dur": 8, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228216, "dur": 8, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228226, "dur": 10, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228238, "dur": 9, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228248, "dur": 9, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228258, "dur": 10, "ph": "X", "name": "ReadAsync 335", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228269, "dur": 13, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228284, "dur": 12, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228298, "dur": 9, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228309, "dur": 21, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228332, "dur": 8, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228341, "dur": 9, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228351, "dur": 8, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228361, "dur": 10, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228373, "dur": 10, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228385, "dur": 14, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228401, "dur": 10, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228412, "dur": 9, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228423, "dur": 9, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228434, "dur": 11, "ph": "X", "name": "ReadAsync 98", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228447, "dur": 11, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228459, "dur": 17, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228478, "dur": 1, "ph": "X", "name": "ProcessMessages 295", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228480, "dur": 33, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228517, "dur": 36, "ph": "X", "name": "ReadAsync 851", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228555, "dur": 16, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228574, "dur": 1, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228576, "dur": 60, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228638, "dur": 1, "ph": "X", "name": "ProcessMessages 737", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228639, "dur": 22, "ph": "X", "name": "ReadAsync 737", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228664, "dur": 14, "ph": "X", "name": "ReadAsync 1112", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228680, "dur": 11, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228693, "dur": 10, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228705, "dur": 9, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228715, "dur": 10, "ph": "X", "name": "ReadAsync 127", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228728, "dur": 10, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228739, "dur": 23, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228764, "dur": 11, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228776, "dur": 9, "ph": "X", "name": "ReadAsync 91", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228787, "dur": 9, "ph": "X", "name": "ReadAsync 118", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228797, "dur": 9, "ph": "X", "name": "ReadAsync 123", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228807, "dur": 12, "ph": "X", "name": "ReadAsync 296", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228820, "dur": 10, "ph": "X", "name": "ReadAsync 406", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228831, "dur": 14, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228846, "dur": 10, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228858, "dur": 11, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228870, "dur": 1, "ph": "X", "name": "ProcessMessages 354", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228872, "dur": 9, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228882, "dur": 10, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228893, "dur": 11, "ph": "X", "name": "ReadAsync 270", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228906, "dur": 9, "ph": "X", "name": "ReadAsync 217", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228917, "dur": 12, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228930, "dur": 10, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228941, "dur": 11, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228953, "dur": 9, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228964, "dur": 9, "ph": "X", "name": "ReadAsync 267", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545228974, "dur": 128, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229104, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229122, "dur": 10, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229134, "dur": 9, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229145, "dur": 12, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229158, "dur": 16, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229176, "dur": 9, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229186, "dur": 8, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229196, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229210, "dur": 12, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229223, "dur": 11, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229236, "dur": 15, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229254, "dur": 10, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229266, "dur": 10, "ph": "X", "name": "ReadAsync 273", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229277, "dur": 11, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229290, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229312, "dur": 19, "ph": "X", "name": "ReadAsync 182", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229334, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229336, "dur": 37, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229376, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229378, "dur": 441, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229821, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229823, "dur": 59, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229883, "dur": 2, "ph": "X", "name": "ProcessMessages 5249", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229897, "dur": 63, "ph": "X", "name": "ReadAsync 5249", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229962, "dur": 1, "ph": "X", "name": "ProcessMessages 4197", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229965, "dur": 19, "ph": "X", "name": "ReadAsync 4197", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229985, "dur": 9, "ph": "X", "name": "ReadAsync 1183", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545229996, "dur": 13, "ph": "X", "name": "ReadAsync 235", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230010, "dur": 10, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230022, "dur": 8, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230031, "dur": 10, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230042, "dur": 10, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230053, "dur": 11, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230066, "dur": 32, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230100, "dur": 10, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230112, "dur": 15, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230130, "dur": 57, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230189, "dur": 61, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230252, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230267, "dur": 13, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230281, "dur": 10, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230293, "dur": 9, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230306, "dur": 16, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230324, "dur": 11, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230337, "dur": 9, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230348, "dur": 8, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230358, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230377, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230405, "dur": 20, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230427, "dur": 17, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230445, "dur": 12, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230459, "dur": 20, "ph": "X", "name": "ReadAsync 375", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230481, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230499, "dur": 23, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230524, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230534, "dur": 9, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230545, "dur": 9, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230555, "dur": 9, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230565, "dur": 12, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230579, "dur": 11, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230592, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230602, "dur": 9, "ph": "X", "name": "ReadAsync 206", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230613, "dur": 10, "ph": "X", "name": "ReadAsync 254", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230624, "dur": 12, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230638, "dur": 10, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230649, "dur": 9, "ph": "X", "name": "ReadAsync 347", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230659, "dur": 9, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230670, "dur": 9, "ph": "X", "name": "ReadAsync 246", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230681, "dur": 132, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230816, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230831, "dur": 12, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230844, "dur": 1, "ph": "X", "name": "ProcessMessages 263", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230846, "dur": 17, "ph": "X", "name": "ReadAsync 263", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230865, "dur": 1, "ph": "X", "name": "ProcessMessages 368", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230867, "dur": 20, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230891, "dur": 13, "ph": "X", "name": "ReadAsync 735", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230906, "dur": 11, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230920, "dur": 8, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230930, "dur": 11, "ph": "X", "name": "ReadAsync 167", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230942, "dur": 9, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230953, "dur": 14, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230968, "dur": 10, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230979, "dur": 10, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545230991, "dur": 11, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231003, "dur": 15, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231020, "dur": 13, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231034, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231045, "dur": 11, "ph": "X", "name": "ReadAsync 279", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231058, "dur": 11, "ph": "X", "name": "ReadAsync 103", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231071, "dur": 12, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231084, "dur": 9, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231095, "dur": 10, "ph": "X", "name": "ReadAsync 221", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231107, "dur": 9, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231119, "dur": 196, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231320, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231346, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231347, "dur": 20, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231370, "dur": 16, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231388, "dur": 14, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231404, "dur": 132, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231538, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231549, "dur": 9, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231559, "dur": 8, "ph": "X", "name": "ReadAsync 271", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231568, "dur": 11, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231581, "dur": 10, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231592, "dur": 15, "ph": "X", "name": "ReadAsync 431", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231608, "dur": 9, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231619, "dur": 9, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231630, "dur": 8, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231639, "dur": 97, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231738, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231748, "dur": 11, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231761, "dur": 9, "ph": "X", "name": "ReadAsync 402", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231771, "dur": 9, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231782, "dur": 27, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231812, "dur": 11, "ph": "X", "name": "ReadAsync 222", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231824, "dur": 10, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231835, "dur": 8, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231845, "dur": 11, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231859, "dur": 109, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231972, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545231994, "dur": 23, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232019, "dur": 16, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232036, "dur": 16, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232054, "dur": 13, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232069, "dur": 15, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232086, "dur": 14, "ph": "X", "name": "ReadAsync 433", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232102, "dur": 16, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232118, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232119, "dur": 14, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232135, "dur": 14, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232151, "dur": 111, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232265, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232296, "dur": 23, "ph": "X", "name": "ReadAsync 546", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232322, "dur": 16, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232341, "dur": 17, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232359, "dur": 13, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232373, "dur": 118, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232495, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232519, "dur": 21, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232542, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232543, "dur": 44, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232588, "dur": 16, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232607, "dur": 14, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232623, "dur": 13, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232638, "dur": 12, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232651, "dur": 13, "ph": "X", "name": "ReadAsync 163", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232666, "dur": 16, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232683, "dur": 13, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232698, "dur": 18, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232717, "dur": 13, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232731, "dur": 164, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232898, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232914, "dur": 13, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232928, "dur": 10, "ph": "X", "name": "ReadAsync 355", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232939, "dur": 9, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232950, "dur": 10, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232961, "dur": 10, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232973, "dur": 10, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232984, "dur": 12, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545232998, "dur": 20, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233022, "dur": 122, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233146, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233170, "dur": 11, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233183, "dur": 10, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233196, "dur": 30, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233228, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233230, "dur": 32, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233264, "dur": 17, "ph": "X", "name": "ReadAsync 1093", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233282, "dur": 10, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233294, "dur": 10, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233305, "dur": 15, "ph": "X", "name": "ReadAsync 195", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233322, "dur": 15, "ph": "X", "name": "ReadAsync 294", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233338, "dur": 10, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233349, "dur": 123, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233475, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233530, "dur": 28, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233559, "dur": 1, "ph": "X", "name": "ProcessMessages 1669", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233561, "dur": 376, "ph": "X", "name": "ReadAsync 1669", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233939, "dur": 41, "ph": "X", "name": "ReadAsync 93", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233981, "dur": 1, "ph": "X", "name": "ProcessMessages 4212", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545233983, "dur": 66, "ph": "X", "name": "ReadAsync 4212", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234054, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234085, "dur": 1, "ph": "X", "name": "ProcessMessages 648", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234087, "dur": 18, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234107, "dur": 24, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234132, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234133, "dur": 22, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234157, "dur": 1, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234159, "dur": 20, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234182, "dur": 13, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234196, "dur": 10, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234207, "dur": 40, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234250, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234269, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234271, "dur": 113, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234386, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234401, "dur": 9, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234411, "dur": 21, "ph": "X", "name": "ReadAsync 311", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234436, "dur": 27, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234466, "dur": 23, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234491, "dur": 1, "ph": "X", "name": "ProcessMessages 683", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234493, "dur": 54, "ph": "X", "name": "ReadAsync 683", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234550, "dur": 22, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234575, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234577, "dur": 103, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234682, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234716, "dur": 68, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234787, "dur": 1, "ph": "X", "name": "ProcessMessages 886", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234788, "dur": 18, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234809, "dur": 14, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234825, "dur": 1, "ph": "X", "name": "ProcessMessages 284", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234827, "dur": 11, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234840, "dur": 22, "ph": "X", "name": "ReadAsync 72", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234865, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234866, "dur": 27, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234897, "dur": 18, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234917, "dur": 22, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234944, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545234969, "dur": 35, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235008, "dur": 19, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235028, "dur": 20, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235050, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235051, "dur": 24, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235078, "dur": 12, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235092, "dur": 16, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235112, "dur": 21, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235135, "dur": 25, "ph": "X", "name": "ReadAsync 266", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235162, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235163, "dur": 21, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235187, "dur": 30, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235219, "dur": 1, "ph": "X", "name": "ProcessMessages 341", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235221, "dur": 23, "ph": "X", "name": "ReadAsync 341", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235246, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235278, "dur": 1, "ph": "X", "name": "ProcessMessages 734", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235279, "dur": 51, "ph": "X", "name": "ReadAsync 734", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235333, "dur": 20, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235355, "dur": 156, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235515, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545235590, "dur": 465, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236063, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236092, "dur": 298, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236392, "dur": 45, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236439, "dur": 2, "ph": "X", "name": "ProcessMessages 852", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236442, "dur": 23, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236468, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236486, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236510, "dur": 9, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236520, "dur": 46, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236569, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236571, "dur": 17, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236590, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236591, "dur": 21, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236614, "dur": 114, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236730, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236732, "dur": 23, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236756, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236758, "dur": 22, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236782, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236819, "dur": 39, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236862, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236878, "dur": 9, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236889, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236917, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545236988, "dur": 19, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237009, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237011, "dur": 90, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237106, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237128, "dur": 31, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237163, "dur": 12, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237177, "dur": 19, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237200, "dur": 14, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237217, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237240, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237265, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237267, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237296, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237298, "dur": 14, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237314, "dur": 9, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237325, "dur": 18, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237348, "dur": 14, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237364, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237399, "dur": 15, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237416, "dur": 8, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237427, "dur": 10, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237440, "dur": 13, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237456, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237479, "dur": 11, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237493, "dur": 18, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237513, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237515, "dur": 33, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237553, "dur": 31, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237587, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237589, "dur": 29, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237620, "dur": 34, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237658, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237660, "dur": 36, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237698, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237701, "dur": 28, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237732, "dur": 24, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237759, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237761, "dur": 23, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237787, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237789, "dur": 15, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237807, "dur": 35, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237846, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237871, "dur": 40, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237914, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237915, "dur": 25, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237943, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237945, "dur": 21, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545237970, "dur": 67, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238040, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238042, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238075, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238077, "dur": 18, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238098, "dur": 18, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238121, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238143, "dur": 145, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238295, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238308, "dur": 16, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238326, "dur": 129, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238459, "dur": 83, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238545, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238577, "dur": 20, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238599, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545238615, "dur": 7830, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545246453, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545246456, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545246502, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545246505, "dur": 21, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545246529, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545246549, "dur": 11, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545246561, "dur": 149, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545246715, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545246800, "dur": 609, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247413, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247441, "dur": 25, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247470, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247485, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247525, "dur": 7, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247533, "dur": 213, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247749, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247844, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247865, "dur": 68, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247936, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247960, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545247987, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248031, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248046, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248081, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248093, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248214, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248231, "dur": 20, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248253, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248268, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248299, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248316, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248360, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248388, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248414, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248435, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248455, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248493, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248511, "dur": 14, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248529, "dur": 118, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248649, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248667, "dur": 86, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248757, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248774, "dur": 113, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248890, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248909, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248928, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248964, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545248985, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249003, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249104, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249126, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249148, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249186, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249198, "dur": 19, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249219, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249235, "dur": 16, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249253, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249272, "dur": 7, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249281, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249295, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249312, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249342, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249358, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249396, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249415, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249446, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249448, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249475, "dur": 12, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249489, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249525, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249544, "dur": 16, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249565, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249583, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249611, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249633, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249692, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249713, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249813, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249837, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249871, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249888, "dur": 10, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249900, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249922, "dur": 18, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545249942, "dur": 183, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250129, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250147, "dur": 57, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250207, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250222, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250252, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250270, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250296, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250312, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250358, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250370, "dur": 19, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250392, "dur": 12, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250406, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250469, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250486, "dur": 120, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250609, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250628, "dur": 17, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250648, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250665, "dur": 13, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250681, "dur": 14, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250698, "dur": 9, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250709, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250740, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250756, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250780, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250802, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250824, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250842, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250860, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250885, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250907, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250925, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250962, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250974, "dur": 18, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545250994, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251013, "dur": 18, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251036, "dur": 15, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251054, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251071, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251090, "dur": 18, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251112, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251135, "dur": 15, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251153, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251171, "dur": 17, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251191, "dur": 21, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251215, "dur": 17, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251235, "dur": 22, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251259, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251261, "dur": 19, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251284, "dur": 18, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251305, "dur": 17, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251325, "dur": 14, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251342, "dur": 17, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251362, "dur": 15, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251380, "dur": 13, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251396, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251429, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251449, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251471, "dur": 9, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251482, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251514, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251533, "dur": 20, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251556, "dur": 19, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251579, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251604, "dur": 13, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251619, "dur": 94, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251717, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251730, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251821, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251841, "dur": 50, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251894, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251916, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251962, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251982, "dur": 13, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251996, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545251998, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252024, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252046, "dur": 14, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252063, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252095, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252117, "dur": 15, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252135, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252155, "dur": 77, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252235, "dur": 88, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252325, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252326, "dur": 26, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252354, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252356, "dur": 75, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252433, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252435, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252459, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252560, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252586, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252611, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252636, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252661, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252681, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252705, "dur": 19, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252728, "dur": 158, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252888, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252905, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252908, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252933, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252958, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545252987, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253020, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253041, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253063, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253081, "dur": 236, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253320, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253338, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253372, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253386, "dur": 66, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253456, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253481, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253499, "dur": 174, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253677, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253708, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253727, "dur": 12, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253744, "dur": 113, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253860, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253889, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253915, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253939, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253961, "dur": 11, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545253973, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254161, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254186, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254214, "dur": 16, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254233, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254280, "dur": 10, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254292, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254330, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254350, "dur": 12, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254364, "dur": 126, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254492, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254511, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254534, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254557, "dur": 389, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254949, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254971, "dur": 15, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545254987, "dur": 1313, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545256304, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545256306, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545256337, "dur": 446, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369545274002, "dur": 17122532, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562396545, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562396548, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562396599, "dur": 7663, "ph": "X", "name": "ProcessMessages 13573", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562404265, "dur": 6256, "ph": "X", "name": "ReadAsync 13573", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562410527, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562410530, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562410547, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562410549, "dur": 3270, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562413827, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562413830, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562413851, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562413875, "dur": 5294, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562419177, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562419179, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562419216, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562419219, "dur": 523972, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562943201, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562943204, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562943249, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562943253, "dur": 2692, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562945953, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562945956, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562946003, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369562946023, "dur": 4136506, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567082535, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567082537, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567082559, "dur": 882, "ph": "X", "name": "ProcessMessages 10628", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567083442, "dur": 2431, "ph": "X", "name": "ReadAsync 10628", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567085876, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567085878, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567085896, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567085897, "dur": 841, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567086743, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567086744, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567086767, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567086781, "dur": 219374, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567306162, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567306165, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567306179, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567306180, "dur": 1023, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567307206, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567307208, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567307252, "dur": 19, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567307272, "dur": 517, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567307794, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567307824, "dur": 411, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21572, "tid": 12884901888, "ts": 1756369567308237, "dur": 6077, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21572, "tid": 429, "ts": 1756369567323127, "dur": 631, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21572, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21572, "tid": 8589934592, "ts": 1756369545174712, "dur": 113981, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21572, "tid": 8589934592, "ts": 1756369545288695, "dur": 2, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21572, "tid": 8589934592, "ts": 1756369545288698, "dur": 791, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21572, "tid": 429, "ts": 1756369567323759, "dur": 3, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21572, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21572, "tid": 4294967296, "ts": 1756369545020742, "dur": 22295152, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21572, "tid": 4294967296, "ts": 1756369545024785, "dur": 146248, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21572, "tid": 4294967296, "ts": 1756369567315910, "dur": 2705, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21572, "tid": 4294967296, "ts": 1756369567317449, "dur": 25, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21572, "tid": 4294967296, "ts": 1756369567318661, "dur": 10, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21572, "tid": 429, "ts": 1756369567323763, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1756369545182036, "dur": 21236, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756369545203296, "dur": 9643, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756369545213046, "dur": 55, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1756369545213101, "dur": 609, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756369545214430, "dur": 8390, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_0F097F89149B6604.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756369545223890, "dur": 240, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1756369545232794, "dur": 54, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ToonyColorsPro2.Demo.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1756369545235217, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-firstpass.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1756369545213726, "dur": 22050, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756369545235793, "dur": 22071784, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756369567307577, "dur": 175, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756369567308048, "dur": 1406, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1756369545213561, "dur": 22240, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545235835, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756369545236066, "dur": 265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1756369545235824, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_59D59D2570B707D8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756369545236332, "dur": 445, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545236778, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_59D59D2570B707D8.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756369545236871, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1756369545237048, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1756369545237331, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545237397, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1756369545237900, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545238055, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545238108, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/LeTai.TrueShadow.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1756369545238293, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545238676, "dur": 565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545239241, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545239832, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545240557, "dur": 521, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Shapes\\Scripts\\Runtime\\Immediate Mode\\Draw.cs"}}, {"pid": 12345, "tid": 1, "ts": 1756369545240536, "dur": 989, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545241525, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545241748, "dur": 162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545241911, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545242474, "dur": 642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545243116, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545243695, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545244728, "dur": 570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545245298, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545246056, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545246800, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545247700, "dur": 537, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545248243, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tiny.Rush.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1756369545249185, "dur": 264, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756369545248699, "dur": 949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tiny.Rush.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756369545249648, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545249904, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756369545250059, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756369545250348, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756369545250580, "dur": 344, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756369545251131, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756369545251231, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 1, "ts": 1756369545249745, "dur": 1754, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tiny.Rush.Tests.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1756369545251499, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545251772, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545251843, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545252015, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545252484, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545252616, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545252868, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545252954, "dur": 212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545253187, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545253264, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545253617, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545253971, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545254153, "dur": 321, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545254474, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369545254790, "dur": 17154986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1756369562409777, "dur": 4897750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545213612, "dur": 22220, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545235846, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756369545236019, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1756369545235835, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_24A8B6F9BAE245F7.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756369545236326, "dur": 537, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545236865, "dur": 380, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756369545237287, "dur": 246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756369545237553, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756369545237751, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756369545237951, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.rsp2"}}, {"pid": 12345, "tid": 2, "ts": 1756369545238034, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545238222, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1756369545238275, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545238346, "dur": 503, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545238849, "dur": 405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545239254, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545241021, "dur": 668, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Editor\\SplineEditor\\SplineEditor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756369545240402, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545242014, "dur": 682, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Animancer\\Internal\\Controller States\\Float1ControllerState.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756369545241700, "dur": 1084, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545242784, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545244011, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545245405, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Flow\\Connections\\UnitConnection.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756369545244661, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545245910, "dur": 744, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545246655, "dur": 84, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545246794, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545247693, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545248078, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756369545248440, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545248676, "dur": 417, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Windows\\WinSDK.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756369545249184, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756369545249763, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Animation\\AnimationOutputWeightProcessor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756369545249850, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\AssetUpgrade\\AnimationTrackUpgrade.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756369545249904, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\AssetUpgrade\\ClipUpgrade.cs"}}, {"pid": 12345, "tid": 2, "ts": 1756369545248636, "dur": 1635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756369545250271, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545250470, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1756369545251046, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756369545251214, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756369545251446, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756369545251749, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756369545251856, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 2, "ts": 1756369545250731, "dur": 1785, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1756369545252516, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545252612, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545252872, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545252947, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545253170, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545253258, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545253619, "dur": 361, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545253980, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545254156, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545254459, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369545254782, "dur": 17154985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1756369562409768, "dur": 4897756, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545213664, "dur": 22241, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545235911, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756369545236057, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1756369545235908, "dur": 369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_1542A8759536CB9C.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756369545236319, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545236492, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545236858, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1756369545237184, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1756369545237465, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1756369545237989, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/MeshBakerCore.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1756369545238270, "dur": 277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545238553, "dur": 324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545238877, "dur": 175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545239052, "dur": 126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545239179, "dur": 1106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545240286, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545240485, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_SpineMG2\\Editor\\spine-unity\\Editor\\Components\\PointFollowerInspector.cs"}}, {"pid": 12345, "tid": 3, "ts": 1756369545240485, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545241350, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545241799, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545242002, "dur": 789, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545242792, "dur": 785, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545243578, "dur": 641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545244219, "dur": 614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545244833, "dur": 635, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545245469, "dur": 655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545246124, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545246782, "dur": 913, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545247696, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545248073, "dur": 1644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756369545249718, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545249776, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756369545250422, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545250554, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756369545250672, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545251215, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756369545251357, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756369545251443, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756369545251811, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 3, "ts": 1756369545250891, "dur": 1214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756369545252106, "dur": 451, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545252609, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1756369545252700, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1756369545252965, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545253179, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545253263, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545253615, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545253962, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545254143, "dur": 295, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545254474, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369545254789, "dur": 17155005, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1756369562409794, "dur": 4897760, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545213578, "dur": 22238, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545235884, "dur": 351, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1756369545235824, "dur": 412, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_7C2C4F34C6B423AF.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756369545236245, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545236357, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756369545236355, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_32007B539749BD9F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756369545236634, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1756369545236613, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_90AA0436019DA739.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756369545236839, "dur": 256, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1756369545237136, "dur": 368, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1756369545237584, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545237743, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756369545238031, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545238112, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Tiny.Rush.Tests.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1756369545238376, "dur": 205, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545238585, "dur": 590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545239175, "dur": 1111, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545240286, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545241023, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545242103, "dur": 190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545242293, "dur": 1136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545243429, "dur": 663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545244092, "dur": 805, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545244898, "dur": 858, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545245756, "dur": 739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545246496, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545247402, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545247697, "dur": 552, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545248254, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/MeshBakerCore.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1756369545248614, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\UnityChannel\\UnityStore.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756369545248561, "dur": 969, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/MeshBakerCore.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756369545249531, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545250065, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756369545250346, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756369545249734, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/MeshBakerEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756369545250672, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545251257, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756369545251357, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 4, "ts": 1756369545250734, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CY.LWGUI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1756369545251740, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545251870, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545252414, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545252587, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545252848, "dur": 100, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545252948, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545253184, "dur": 67, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545253251, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545253611, "dur": 351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545253962, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545254140, "dur": 297, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545254471, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369545254783, "dur": 17155014, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1756369562409798, "dur": 4897919, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545213610, "dur": 22215, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545235842, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756369545236047, "dur": 436, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1756369545235833, "dur": 650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_BDC059FEE0C56713.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756369545236542, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545236846, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756369545237173, "dur": 263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1756369545237493, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1756369545238059, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545238259, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545238336, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545239028, "dur": 475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545239504, "dur": 499, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545240003, "dur": 401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545240422, "dur": 511, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Editor\\Sample Modifiers\\SplineSampleModifierEditor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1756369545240404, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545241324, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545241689, "dur": 336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545242025, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545242990, "dur": 645, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545243635, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545244570, "dur": 538, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545245108, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545246021, "dur": 856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545246877, "dur": 815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545247692, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545248075, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756369545248171, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545248384, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756369545248235, "dur": 1010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756369545249245, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545249511, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545249606, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/ShapesRuntime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756369545249953, "dur": 1017, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ShapesRuntime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756369545250970, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545251160, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Windows\\Q1SDKUnityPlugin.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756369545251357, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756369545251994, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 5, "ts": 1756369545251042, "dur": 1183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ShapesSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1756369545252226, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545252318, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545252504, "dur": 84, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545252588, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545252851, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545252944, "dur": 227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545253171, "dur": 77, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545253319, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545253613, "dur": 347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545253979, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545254153, "dur": 303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545254457, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1756369545254602, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369545254779, "dur": 17155004, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1756369562409783, "dur": 4897759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545213631, "dur": 22217, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545235895, "dur": 692, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1756369545235856, "dur": 732, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_3D07E2B87C74DF17.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756369545236633, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545236861, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1756369545237146, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756369545237315, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756369545237697, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545237922, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545238042, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545238125, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1756369545238377, "dur": 611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545238988, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545239720, "dur": 398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545240118, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545240465, "dur": 506, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_TrueShadow\\Scripts\\Editor\\SpreadSliderDrawer.cs"}}, {"pid": 12345, "tid": 6, "ts": 1756369545240332, "dur": 755, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545241087, "dur": 473, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545241561, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545242253, "dur": 1064, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545243318, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545244213, "dur": 567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545244780, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545245632, "dur": 766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545246398, "dur": 604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545247002, "dur": 690, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545247693, "dur": 633, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545248331, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756369545248600, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545248696, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/ToonyColorsPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1756369545248934, "dur": 945, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545250110, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1756369545249883, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ToonyColorsPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1756369545250882, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545251039, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545251179, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545251436, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545251643, "dur": 113, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545251757, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\easytouch.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1756369545251757, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/easytouch.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1756369545251865, "dur": 393, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545252259, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545252613, "dur": 270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545252883, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545252958, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545253168, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545253251, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545253630, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545253968, "dur": 182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545254150, "dur": 284, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545254466, "dur": 315, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369545254781, "dur": 17155003, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1756369562409785, "dur": 4897783, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545213656, "dur": 22244, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545235908, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756369545236072, "dur": 406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1756369545235904, "dur": 575, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_8D0CB068D769A31A.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756369545236573, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545236849, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756369545237177, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756369545237454, "dur": 326, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ShapesRuntime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1756369545237865, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545238056, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UIEffect.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1756369545238344, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545239122, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545239705, "dur": 451, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545240156, "dur": 350, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545240506, "dur": 1639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545242145, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545242345, "dur": 828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545243173, "dur": 896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545244069, "dur": 653, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545244722, "dur": 552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545245275, "dur": 983, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545246278, "dur": 90, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545246368, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545247008, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545247736, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545248336, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756369545248498, "dur": 705, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545249209, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756369545249496, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545249550, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/ToonyColorsPro2.Demo.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1756369545249703, "dur": 1163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545251357, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 7, "ts": 1756369545250872, "dur": 1082, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ToonyColorsPro2.Demo.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1756369545251954, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545252042, "dur": 585, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545252631, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545252850, "dur": 97, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545252947, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545253185, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545253259, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545253617, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545253971, "dur": 183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545254154, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545254461, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545254774, "dur": 36548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369545291322, "dur": 17118434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1756369562409756, "dur": 4897818, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545213688, "dur": 22222, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545235920, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1756369545236112, "dur": 423, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1756369545235915, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_7B26F11653A8BB46.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756369545236625, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1756369545236624, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_6A4C4ABC6FBD89C0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756369545236758, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1756369545237025, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp2"}}, {"pid": 12345, "tid": 8, "ts": 1756369545237410, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756369545237715, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545238007, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/easytouch.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1756369545238106, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545238291, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545238922, "dur": 650, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545239573, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545239864, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545241177, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545241644, "dur": 603, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545242248, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545242972, "dur": 737, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545243709, "dur": 695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545244404, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545245232, "dur": 622, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545245854, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545246493, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545247432, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545247700, "dur": 494, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545248200, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756369545248518, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756369545249292, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545249359, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tiny.Rush.Tests.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756369545249524, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545249658, "dur": 374, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/LeTai.TrueShadow.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1756369545250514, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1756369545250068, "dur": 1132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/LeTai.TrueShadow.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756369545251201, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545251442, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Windows\\WinSDK.dll"}}, {"pid": 12345, "tid": 8, "ts": 1756369545251663, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 8, "ts": 1756369545251759, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1756369545251375, "dur": 931, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/LeTai.TrueShadow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1756369545252306, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545252400, "dur": 178, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545252597, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545252862, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545252964, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545253180, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545253254, "dur": 380, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545253634, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545253977, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545254149, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545254462, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369545254777, "dur": 17154989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1756369562409766, "dur": 4897780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545213751, "dur": 22179, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545235942, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756369545236024, "dur": 368, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1756369545235937, "dur": 456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_7F3AF681D6CECB6F.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756369545236454, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545236593, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545236870, "dur": 487, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756369545237394, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756369545237731, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756369545237814, "dur": 292, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756369545238151, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1756369545238351, "dur": 821, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545239172, "dur": 445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545239617, "dur": 291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545239908, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545240535, "dur": 1203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545241739, "dur": 414, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545242195, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\SolutionProjectEntry.cs"}}, {"pid": 12345, "tid": 9, "ts": 1756369545242153, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545243523, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545244255, "dur": 566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545244821, "dur": 619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545245440, "dur": 827, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545246268, "dur": 55, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545246385, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545246933, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545247717, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545248074, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756369545248614, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756369545248296, "dur": 787, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756369545249084, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545249182, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756369545249490, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545249708, "dur": 306, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756369545250111, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756369545249562, "dur": 1235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756369545250798, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545250903, "dur": 290, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1756369545251193, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545251388, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Koreographer\\Plugins\\Koreographer\\SonicBloom.MIDI.dll"}}, {"pid": 12345, "tid": 9, "ts": 1756369545251270, "dur": 1007, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1756369545252277, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545252421, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545252592, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545252860, "dur": 91, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545252951, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545253170, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545253253, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545253618, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545253980, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545254152, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545254464, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369545254776, "dur": 17154982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1756369562409758, "dur": 4897792, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545213716, "dur": 22201, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545235926, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1756369545236052, "dur": 503, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1756369545235924, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_4FFE5013DEBA169E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756369545236556, "dur": 315, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545236880, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756369545237334, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756369545237529, "dur": 209, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Obi.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1756369545237738, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545237896, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545238039, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545238223, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1756369545238481, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545238724, "dur": 806, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545239530, "dur": 702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545240233, "dur": 675, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545240908, "dur": 713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545241621, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545242169, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545242825, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545243651, "dur": 651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545244303, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545245176, "dur": 521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545245698, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545246394, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545247003, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545247698, "dur": 620, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545248321, "dur": 479, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/LeTai.TrueShadow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756369545248800, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545248906, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756369545249485, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1756369545249023, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756369545249707, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/ShapesSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756369545249991, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545250111, "dur": 435, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Parse\\Plugins\\dotNet45\\Unity.Tasks.dll"}}, {"pid": 12345, "tid": 10, "ts": 1756369545251257, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 10, "ts": 1756369545250089, "dur": 1346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756369545251436, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545251528, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545251692, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545251868, "dur": 483, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545252369, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545252600, "dur": 271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545252872, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.DocCodeExamples.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1756369545252978, "dur": 259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.DocCodeExamples.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1756369545253237, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545253306, "dur": 317, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545253624, "dur": 339, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545253963, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545254144, "dur": 292, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545254472, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369545254780, "dur": 17154980, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1756369562409761, "dur": 4897782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545214113, "dur": 22142, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545236291, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545236460, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545236623, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756369545236622, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_38431C43644D6EBD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756369545236898, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1756369545237143, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756369545237382, "dur": 336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756369545237718, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545237819, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Obi.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756369545238281, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756369545238501, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545238725, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1756369545238825, "dur": 197, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545239022, "dur": 470, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545240525, "dur": 579, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_ChuagnYi\\Script\\Neery\\Pooling\\QueuePool.cs"}}, {"pid": 12345, "tid": 11, "ts": 1756369545239493, "dur": 1707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545241201, "dur": 339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545241541, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545242151, "dur": 385, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545242537, "dur": 708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545243245, "dur": 852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545244098, "dur": 670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545244768, "dur": 613, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545245381, "dur": 786, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545246167, "dur": 525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545246714, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545246789, "dur": 910, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545247699, "dur": 490, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545248194, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756369545248756, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/spinemg2-unity-editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756369545249011, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Obi.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1756369545249238, "dur": 587, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545249850, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DOTween\\DOTween50.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756369545250110, "dur": 435, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756369545250911, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756369545251029, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756369545251263, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 11, "ts": 1756369545249839, "dur": 1669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/easytouch.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1756369545251508, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545251775, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545251877, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545252471, "dur": 133, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545252604, "dur": 249, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545252853, "dur": 89, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545252942, "dur": 231, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545253174, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545253256, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545253621, "dur": 352, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545253974, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545254156, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545254467, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369545254790, "dur": 17155001, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1756369562409792, "dur": 4897734, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545213767, "dur": 22181, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545235954, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1756369545236077, "dur": 483, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1756369545235952, "dur": 609, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_F5D6C7D883717C04.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756369545236607, "dur": 269, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545236883, "dur": 299, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1756369545237233, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756369545237645, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756369545237993, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Tiny.Rush.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1756369545238302, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545239206, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545240344, "dur": 711, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\Common\\LogMan.cs"}}, {"pid": 12345, "tid": 12, "ts": 1756369545239942, "dur": 1183, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545241125, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545241585, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545242782, "dur": 667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545243449, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545244170, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545244854, "dur": 661, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545245516, "dur": 853, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545246369, "dur": 756, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545247126, "dur": 596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545247722, "dur": 425, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545248153, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756369545248494, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545248909, "dur": 752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756369545249661, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545249806, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Windows\\System.Text.Encoding.CodePages.dll"}}, {"pid": 12345, "tid": 12, "ts": 1756369545250064, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1756369545249731, "dur": 1025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/IngameDebugConsole.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756369545250757, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545250884, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756369545251193, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545251259, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DemiLib\\DemiLib.dll"}}, {"pid": 12345, "tid": 12, "ts": 1756369545251533, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1756369545251718, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1756369545251258, "dur": 996, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756369545252254, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545252486, "dur": 109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545252595, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545252861, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545252955, "dur": 219, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545253174, "dur": 75, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545253249, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545253714, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1756369545253988, "dur": 154, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545254142, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545254464, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1756369545254609, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369545254784, "dur": 17154989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1756369562409774, "dur": 4897785, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545213787, "dur": 22176, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545235967, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1756369545236062, "dur": 344, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1756369545235966, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_FFCA22B00416E73E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756369545236408, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545236628, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1756369545236627, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_AEE611DA6D6531BB.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756369545236803, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545236887, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756369545237596, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545237717, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545237790, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/AppleAuth.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756369545238046, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545238142, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/MeshBakerCore.Examples.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1756369545238375, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545238550, "dur": 707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545239257, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545240253, "dur": 848, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\CasualGame\\DLYShuiGuan\\Script\\Data\\UIData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1756369545239840, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545241118, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545241359, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545241875, "dur": 465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545242340, "dur": 1018, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545243358, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545244224, "dur": 829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545245053, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545245736, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545246644, "dur": 105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545246801, "dur": 900, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545247701, "dur": 452, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545248158, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756369545248615, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1756369545248491, "dur": 892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/AppleAuth.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756369545249383, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545249446, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Obi.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1756369545249851, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1756369545250065, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1756369545249737, "dur": 1688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Obi.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756369545251426, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545251840, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Obi.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1756369545252496, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545252614, "dur": 268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545252883, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545252949, "dur": 226, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545253175, "dur": 80, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545253255, "dur": 367, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545253622, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545253983, "dur": 174, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545254157, "dur": 290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545254464, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369545254775, "dur": 17154977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369562409753, "dur": 4676183, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1756369567085938, "dur": 220436, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1756369567085937, "dur": 220439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 13, "ts": 1756369567306392, "dur": 1069, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1756369545213805, "dur": 22162, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545235974, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1756369545236059, "dur": 360, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1756369545235973, "dur": 447, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_389F212FAF489129.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756369545236564, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545236876, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 14, "ts": 1756369545237006, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1756369545237371, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756369545237666, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545237788, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545237965, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1756369545238147, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1756369545238491, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545238722, "dur": 814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545239536, "dur": 671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545240526, "dur": 751, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\DocCodeExamples\\VariableExamples.cs"}}, {"pid": 12345, "tid": 14, "ts": 1756369545240207, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545241469, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545242109, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545242287, "dur": 429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545242716, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545243686, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545244382, "dur": 652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545245035, "dur": 561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545245596, "dur": 540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545246136, "dur": 483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545246723, "dur": 62, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545246785, "dur": 920, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545247705, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545248229, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/spinemg2-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756369545248676, "dur": 309, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1756369545249459, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.GradleProject.dll"}}, {"pid": 12345, "tid": 14, "ts": 1756369545248482, "dur": 1341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/spinemg2-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756369545249824, "dur": 302, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545250780, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 14, "ts": 1756369545250133, "dur": 1012, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/spinemg2-unity-editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756369545251146, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545251607, "dur": 280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756369545251918, "dur": 1178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756369545253259, "dur": 289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756369545253643, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756369545253745, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756369545254223, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756369545254452, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1756369545254535, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756369545254769, "dur": 644, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756369545256113, "dur": 187, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369545256562, "dur": 17140867, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756369562410281, "dur": 8409, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 14, "ts": 1756369562409782, "dur": 9189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756369562419292, "dur": 92, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1756369562419396, "dur": 4664084, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1756369567085902, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1756369567085901, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1756369567086127, "dur": 876, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 14, "ts": 1756369567087006, "dur": 220661, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369545213818, "dur": 22160, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369545235990, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545236115, "dur": 331, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1756369545235984, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_58980D046BFD513D.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756369545236515, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369545236620, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369545236842, "dur": 396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1756369545237273, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756369545238073, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545238375, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545238680, "dur": 366, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545239296, "dur": 432, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545239816, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545240050, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545240318, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545240551, "dur": 571, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545241122, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545241264, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545241323, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545241403, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545241672, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545241862, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545242147, "dur": 512, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545242761, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545242966, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545243421, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityCombinatorialStrategy.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545243838, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545244092, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\AssemblyNameFilter.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545244289, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545244388, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\DefaultTestWorkItem.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545244548, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\TestCommandBuilder.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545244619, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545244932, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\PlayerQuitHandler.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545245019, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\PlayModeRunnerCallback.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545245205, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Messages\\IEditModeTestYieldInstruction.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545245606, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestPlatform.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545245818, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AttributeHelper.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545246078, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\IMonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545246155, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\PostBuildCleanupAttribute.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545246231, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\QuaternionEqualityComparer.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545246352, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackListener.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545246445, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Utils.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545246522, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector2EqualityComparer.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545237367, "dur": 9293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756369545246661, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369545246822, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756369545247150, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545247302, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545246964, "dur": 655, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756369545247787, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756369545248074, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756369545248615, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545248778, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1756369545249850, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Ensure\\Ensure.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545250111, "dur": 439, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Listeners\\IGraphEventListener.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545251132, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Runtime\\VisualScripting.Core\\Serialization\\Converters\\RayConverter.cs"}}, {"pid": 12345, "tid": 15, "ts": 1756369545248478, "dur": 2997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756369545251476, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369545251606, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756369545251875, "dur": 899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756369545252871, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1756369545252943, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1756369545253277, "dur": 353, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369545253631, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369545253970, "dur": 179, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369545254150, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369545254468, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369545254797, "dur": 17154983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1756369562409780, "dur": 4897765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545213832, "dur": 22153, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545235998, "dur": 455, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1756369545235987, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_FEB44C0922F521E3.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756369545236453, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545236532, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545236904, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1756369545237319, "dur": 472, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756369545237851, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545238283, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756369545238421, "dur": 312, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545238734, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UIEffect-Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1756369545238836, "dur": 137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545238973, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545239323, "dur": 783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545240106, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545241024, "dur": 677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545241702, "dur": 1221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545242923, "dur": 1484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545244408, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545245183, "dur": 531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545245714, "dur": 749, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545246463, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545247054, "dur": 655, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545247709, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545248099, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1756369545248265, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545248384, "dur": 288, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756369545248677, "dur": 654, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756369545248341, "dur": 1708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756369545250049, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545250198, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DOTween\\DOTween46.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756369545250535, "dur": 490, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\UnityChannel\\ChannelPurchase.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756369545251285, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756369545251469, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756369545251579, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756369545251663, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 16, "ts": 1756369545250193, "dur": 2103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/spinemg-unity.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1756369545252363, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545252591, "dur": 258, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545252849, "dur": 114, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545252963, "dur": 220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545253183, "dur": 63, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545253269, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545253620, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545253985, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545254151, "dur": 318, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545254469, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369545254781, "dur": 17154983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1756369562409765, "dur": 4897806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545213851, "dur": 22173, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545236046, "dur": 388, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1756369545236027, "dur": 408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_F8FEAE19598E5B0E.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756369545236436, "dur": 286, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545236755, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1756369545236961, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545237022, "dur": 313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 17, "ts": 1756369545237443, "dur": 176, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756369545237637, "dur": 273, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1756369545238348, "dur": 1660, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545240008, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545240416, "dur": 688, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Editor\\Components\\TubeGeneratorEditor.cs"}}, {"pid": 12345, "tid": 17, "ts": 1756369545240408, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545241548, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545242116, "dur": 714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545242830, "dur": 1677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545244507, "dur": 492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545244999, "dur": 535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545245534, "dur": 831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545246366, "dur": 170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545246537, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545246788, "dur": 909, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545247697, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545248076, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756369545248212, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Q1SDK\\protobuf-net.dll"}}, {"pid": 12345, "tid": 17, "ts": 1756369545248624, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 17, "ts": 1756369545248191, "dur": 1081, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756369545249272, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545249394, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1756369545249603, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545249808, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\Windows\\Q1SDKUnityPlugin.dll"}}, {"pid": 12345, "tid": 17, "ts": 1756369545250535, "dur": 508, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Data.dll"}}, {"pid": 12345, "tid": 17, "ts": 1756369545251130, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics.Editor\\MatrixDrawer.cs"}}, {"pid": 12345, "tid": 17, "ts": 1756369545249771, "dur": 1439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1756369545251210, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545251636, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545252075, "dur": 528, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545252603, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545252863, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545252953, "dur": 223, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545253176, "dur": 81, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545253257, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545253608, "dur": 356, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545253964, "dur": 181, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545254145, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545254476, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369545254787, "dur": 17154987, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1756369562409775, "dur": 4897780, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545213865, "dur": 22213, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545236113, "dur": 406, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1756369545236082, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_F61FF965A6F6FAF4.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756369545236553, "dur": 347, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545236909, "dur": 771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756369545237714, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756369545238052, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545238120, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ShapesEditor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756369545238470, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545238757, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ShapesEditor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1756369545238865, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545239265, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545240131, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545240539, "dur": 717, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Dreamteck\\Splines\\Editor\\Components\\BakeMeshWindow.cs"}}, {"pid": 12345, "tid": 18, "ts": 1756369545240439, "dur": 1036, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545241476, "dur": 254, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545241730, "dur": 980, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545242711, "dur": 422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545243207, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Flow\\Connections\\UnitConnectionWidget.cs"}}, {"pid": 12345, "tid": 18, "ts": 1756369545243133, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545244488, "dur": 885, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545245373, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545246523, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545247287, "dur": 442, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545247729, "dur": 325, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545248079, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756369545248410, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545248624, "dur": 401, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1756369545249194, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1756369545249355, "dur": 510, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 18, "ts": 1756369545248480, "dur": 1825, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756369545250305, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545250405, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545250529, "dur": 967, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Animancer.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1756369545251545, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DOTween\\Editor\\DOTweenEditor.dll"}}, {"pid": 12345, "tid": 18, "ts": 1756369545251526, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Animancer.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1756369545252605, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545252859, "dur": 94, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545252953, "dur": 224, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545253177, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545253250, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545253608, "dur": 370, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545253978, "dur": 173, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545254151, "dur": 308, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545254460, "dur": 312, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545254772, "dur": 34970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545289743, "dur": 1575, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369545291319, "dur": 17118467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1756369562409786, "dur": 4897755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545213877, "dur": 22208, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545236120, "dur": 268, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1756369545236088, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_0D716A535EA5D9A0.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756369545236485, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545236877, "dur": 285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756369545237247, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 19, "ts": 1756369545237517, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1756369545237588, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545237745, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ShapesRuntime.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756369545237947, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ShapesRuntime.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756369545238079, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/spinemg2-unity-editor.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1756369545238325, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545238378, "dur": 494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545238872, "dur": 835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545239708, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545240994, "dur": 691, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_JMOAssets\\ToonyColorsPro\\Editor\\Legacy\\TCP2_MaterialInspector_SurfacePBS_SG.cs"}}, {"pid": 12345, "tid": 19, "ts": 1756369545240487, "dur": 1580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545242067, "dur": 800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545244063, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.8\\Editor\\VisualScripting.Flow\\Plugin\\Migrations\\Migration_1_1_1_to_1_1_2.cs"}}, {"pid": 12345, "tid": 19, "ts": 1756369545242868, "dur": 1944, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545244813, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545245360, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545246724, "dur": 70, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545246795, "dur": 899, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545247695, "dur": 617, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545248316, "dur": 275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UIEffect.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756369545248592, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545248677, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Animancer.FSM.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756369545248960, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545249229, "dur": 217, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/MeshBakerCore.Examples.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756369545249481, "dur": 214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/easytouch.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756369545249712, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/ShapesEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1756369545250177, "dur": 889, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1756369545251066, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545251614, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545251770, "dur": 105, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545251875, "dur": 573, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545252469, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545252586, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545252874, "dur": 72, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545252946, "dur": 232, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545253178, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545253252, "dur": 357, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545253609, "dur": 362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545253971, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545254139, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545254473, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369545254779, "dur": 17154990, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1756369562409770, "dur": 4897753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545213897, "dur": 22192, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545236114, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1756369545236093, "dur": 295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_243621AF56FD8BB7.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756369545236558, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545236634, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545236633, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_4C96AFA9785B3E8A.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756369545236847, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1756369545237203, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756369545238172, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545238241, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545238377, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545238627, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545238754, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545239351, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545239556, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545239682, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545239809, "dur": 210, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545240020, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545240071, "dur": 290, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545240367, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545240564, "dur": 1072, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545241669, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545241844, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545242131, "dur": 519, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 20, "ts": 1756369545242897, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\PointerEventData.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545243088, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545243204, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTriggerType.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545243255, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\ExecuteEvents.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545243321, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545243657, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545243755, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelRaycaster.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545243836, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545243972, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Animation\\CoroutineTween.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545244050, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\AnimationTriggers.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545244298, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545244628, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMaskable.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545244740, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\AspectRatioFitter.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545244844, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545244962, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545245064, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ILayoutElement.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545245161, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutGroup.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545245270, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\VerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545245436, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaterialModifiers\\IMaterialModifier.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545245514, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MultipleDisplayUtilities.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545245628, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Scrollbar.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545245843, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpecializedCollections\\IndexedSet.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545245895, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpriteState.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545245950, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\StencilMaterial.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545246020, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Text.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545246136, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Toggle.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545246214, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ToggleGroup.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545246280, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\ReflectionMethodsCache.cs"}}, {"pid": 12345, "tid": 20, "ts": 1756369545237545, "dur": 9035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756369545246582, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545246723, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545246789, "dur": 917, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545247707, "dur": 435, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545248148, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1756369545248474, "dur": 842, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1756369545249317, "dur": 2089, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545251428, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545251648, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545251775, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545251872, "dur": 538, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545252412, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545252488, "dur": 111, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545252599, "dur": 247, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545252846, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545252965, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545253183, "dur": 64, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545253258, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545253607, "dur": 360, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545253967, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545254138, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545254475, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369545254782, "dur": 17155013, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1756369562409796, "dur": 4897874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545213913, "dur": 22204, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545236145, "dur": 248, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 21, "ts": 1756369545236120, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_C549C74F0FF91F05.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756369545236394, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545236593, "dur": 281, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545236881, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 21, "ts": 1756369545237104, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545237209, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545237262, "dur": 314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1756369545237688, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545237762, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1756369545238076, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/spinemg-unity.rsp"}}, {"pid": 12345, "tid": 21, "ts": 1756369545238276, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545238353, "dur": 682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545239035, "dur": 1080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545240115, "dur": 371, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545240503, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_JMOAssets\\ToonyColorsPro\\Editor\\Utils\\TCP2_GUI.cs"}}, {"pid": 12345, "tid": 21, "ts": 1756369545240487, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545241421, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545241912, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545242216, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545243566, "dur": 686, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545244253, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545245166, "dur": 585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545245752, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545246509, "dur": 904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545247414, "dur": 314, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545247728, "dur": 371, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545248100, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/ToonyColorsPro.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756369545248562, "dur": 502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ToonyColorsPro.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756369545249064, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545249463, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 21, "ts": 1756369545249659, "dur": 167, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545250108, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1756369545250327, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 21, "ts": 1756369545250535, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 21, "ts": 1756369545249861, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.Modules.UnityMathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756369545250860, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545250946, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545251257, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 21, "ts": 1756369545251751, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 21, "ts": 1756369545251086, "dur": 941, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/ShapesEditor.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756369545252028, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545252091, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545252282, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545252591, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545252855, "dur": 106, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545252961, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545253177, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545253257, "dur": 348, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545253627, "dur": 342, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545253969, "dur": 176, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545254146, "dur": 296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545254474, "dur": 294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545254782, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor-firstpass.dll (+2 others)"}}, {"pid": 12345, "tid": 21, "ts": 1756369545255144, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369545255223, "dur": 17154567, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 21, "ts": 1756369562409791, "dur": 4897874, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545213927, "dur": 22195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545236153, "dur": 238, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 22, "ts": 1756369545236128, "dur": 264, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_7B54AD0B13F0D210.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756369545236511, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_0870E6DEDB9CCB5C.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756369545236618, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545236889, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1756369545237205, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1756369545237401, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545237484, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 22, "ts": 1756369545237671, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545237998, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ShapesSamples.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 22, "ts": 1756369545238068, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/easytouch.rsp"}}, {"pid": 12345, "tid": 22, "ts": 1756369545238303, "dur": 738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545239041, "dur": 180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545239221, "dur": 712, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545240505, "dur": 1172, "ph": "X", "name": "File", "args": {"detail": "Assets\\Scripts\\EmojiPuzzleInput\\EmojiPuzzleLine.cs"}}, {"pid": 12345, "tid": 22, "ts": 1756369545239933, "dur": 1843, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545241776, "dur": 953, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545242730, "dur": 840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545243570, "dur": 606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545244176, "dur": 734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545244910, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545246238, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545246494, "dur": 622, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\CommandLineTest\\LogWriter.cs"}}, {"pid": 12345, "tid": 22, "ts": 1756369545246442, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545247288, "dur": 414, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545247702, "dur": 497, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545248204, "dur": 1251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756369545249455, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545249566, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DOTweenPro\\Editor\\DOTweenProEditor.dll"}}, {"pid": 12345, "tid": 22, "ts": 1756369545249666, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Parse\\Plugins\\dotNet45\\Unity.Compat.dll"}}, {"pid": 12345, "tid": 22, "ts": 1756369545249850, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1756369545250003, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1756369545249515, "dur": 1143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756369545250658, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545251048, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 22, "ts": 1756369545251442, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 22, "ts": 1756369545251531, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 22, "ts": 1756369545250738, "dur": 1148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Sirenix.OdinInspector.CompatibilityLayer.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 22, "ts": 1756369545251887, "dur": 325, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545252252, "dur": 338, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545252590, "dur": 254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545252890, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545252968, "dur": 214, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545253182, "dur": 79, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545253261, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545253611, "dur": 355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545253966, "dur": 180, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545254146, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545254456, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 22, "ts": 1756369545254591, "dur": 195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369545254787, "dur": 17155012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 22, "ts": 1756369562409800, "dur": 4897877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545213996, "dur": 22141, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545236154, "dur": 258, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 23, "ts": 1756369545236140, "dur": 272, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_A31099604CCBA22C.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756369545236413, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545236624, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545236908, "dur": 245, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756369545237179, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756369545237733, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756369545238138, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/MeshBakerEditor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756369545238405, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545238718, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/MeshBakerEditor.rsp"}}, {"pid": 12345, "tid": 23, "ts": 1756369545238807, "dur": 559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545239366, "dur": 276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545239642, "dur": 262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545239904, "dur": 138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545240042, "dur": 360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545240402, "dur": 528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545240930, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545241239, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Obi\\Scripts\\Common\\Backends\\Oni\\Constraints\\ShapeMatching\\OniShapeMatchingConstraintsImpl.cs"}}, {"pid": 12345, "tid": 23, "ts": 1756369545241150, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545242224, "dur": 728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545242952, "dur": 971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545243923, "dur": 460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545244383, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545245005, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545245688, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545246900, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545247701, "dur": 532, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545248238, "dur": 497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756369545248736, "dur": 1326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545250066, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DemiLib\\DemiLib.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756369545250066, "dur": 756, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1756369545250822, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545250919, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Tiny.Rush.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 23, "ts": 1756369545251350, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\3rd\\Demigiant\\DOTweenPro\\Editor\\DOTweenProEditor.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756369545251590, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756369545251801, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756369545251996, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Gradle.dll"}}, {"pid": 12345, "tid": 23, "ts": 1756369545251343, "dur": 1047, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Tiny.Rush.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 23, "ts": 1756369545252456, "dur": 129, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545252622, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545252856, "dur": 99, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545252955, "dur": 216, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545253171, "dur": 82, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545253254, "dur": 358, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545253612, "dur": 365, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545253978, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545254148, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545254467, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369545254774, "dur": 17154977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 23, "ts": 1756369562409752, "dur": 987, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1756369562409752, "dur": 988, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1756369562410767, "dur": 3253, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 23, "ts": 1756369562414026, "dur": 4893594, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545214004, "dur": 22154, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545236174, "dur": 364, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 24, "ts": 1756369545236161, "dur": 378, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_03C0A16AC7611605.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756369545236630, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545236884, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756369545237137, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756369545237507, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp2"}}, {"pid": 12345, "tid": 24, "ts": 1756369545237609, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545237742, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545237869, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/spinemg2-unity.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756369545238005, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ToonyColorsPro.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 24, "ts": 1756369545238076, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Animancer.rsp"}}, {"pid": 12345, "tid": 24, "ts": 1756369545238312, "dur": 990, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545239302, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545239866, "dur": 178, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545240044, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545240470, "dur": 720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545241191, "dur": 510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545241701, "dur": 303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545242004, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545242272, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545242546, "dur": 408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545242954, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545243711, "dur": 1367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545245078, "dur": 1049, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545246128, "dur": 823, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545246952, "dur": 742, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545247694, "dur": 359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545248080, "dur": 467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 24, "ts": 1756369545248547, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545249185, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\PlaybackEngines\\AndroidPlayer\\Unity.Android.Types.dll"}}, {"pid": 12345, "tid": 24, "ts": 1756369545248621, "dur": 692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1756369545249313, "dur": 407, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545249850, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1756369545250534, "dur": 508, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 24, "ts": 1756369545251131, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\CasualGame.Dreamteck.Utilities.ref.dll"}}, {"pid": 12345, "tid": 24, "ts": 1756369545249724, "dur": 1707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1756369545251432, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545251783, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 24, "ts": 1756369545251502, "dur": 870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/CasualGame.Dreamteck.Splines.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 24, "ts": 1756369545252373, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545252477, "dur": 102, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545252593, "dur": 261, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545252854, "dur": 107, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545252961, "dur": 208, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545253169, "dur": 87, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545253256, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545253610, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545253984, "dur": 172, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545254156, "dur": 310, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545254466, "dur": 319, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369545254785, "dur": 17155011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 24, "ts": 1756369562409797, "dur": 4897857, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545214038, "dur": 22156, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545236199, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_CA117DF3E8BEFDF6.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1756369545236255, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545236344, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 25, "ts": 1756369545236343, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_E8F6954025867CB8.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1756369545236430, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545236495, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545236839, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.rsp2"}}, {"pid": 12345, "tid": 25, "ts": 1756369545237162, "dur": 414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1756369545237623, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1756369545237917, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545237990, "dur": 90, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/FancyScrollView.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 25, "ts": 1756369545238081, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ToonyColorsPro.Editor.rsp"}}, {"pid": 12345, "tid": 25, "ts": 1756369545238310, "dur": 631, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545238941, "dur": 278, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545239220, "dur": 691, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545239911, "dur": 341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545240558, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Obi\\Editor\\Common\\Utils\\ObiParticleAttachmentEditor.cs"}}, {"pid": 12345, "tid": 25, "ts": 1756369545240252, "dur": 873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545241125, "dur": 764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545242117, "dur": 538, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_EasyTouch\\EasyTouch\\Plugins\\Engine\\Finger.cs"}}, {"pid": 12345, "tid": 25, "ts": 1756369545241889, "dur": 777, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545242666, "dur": 537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545243204, "dur": 986, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545244190, "dur": 717, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545244908, "dur": 1158, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545246067, "dur": 757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545246825, "dur": 864, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545247704, "dur": 524, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545248231, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll.mvfrm"}}, {"pid": 12345, "tid": 25, "ts": 1756369545248358, "dur": 417, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545248778, "dur": 560, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1756369545249339, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545249810, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Parse\\Plugins\\dotNet45\\Unity.Tasks.dll"}}, {"pid": 12345, "tid": 25, "ts": 1756369545250537, "dur": 503, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 25, "ts": 1756369545251132, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 25, "ts": 1756369545251284, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 25, "ts": 1756369545249787, "dur": 1651, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Lofelt.NiceVibrations.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 25, "ts": 1756369545251652, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545251855, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545252060, "dur": 492, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545252599, "dur": 248, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545252848, "dur": 121, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545252969, "dur": 213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545253182, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545253260, "dur": 369, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545253629, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545253975, "dur": 166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545254141, "dur": 302, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545254477, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369545254788, "dur": 17155015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 25, "ts": 1756369562409803, "dur": 4897829, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545214058, "dur": 22183, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545236331, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545236444, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545236633, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1756369545236632, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0EED61BE115AECA6.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1756369545236799, "dur": 113, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_0EED61BE115AECA6.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1756369545236919, "dur": 766, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1756369545237685, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545237814, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/LeTai.TrueShadow.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1756369545238052, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545238115, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Obi.Editor.rsp"}}, {"pid": 12345, "tid": 26, "ts": 1756369545238371, "dur": 547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545238919, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545239113, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545239920, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545240231, "dur": 295, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545240527, "dur": 1252, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545241779, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545242373, "dur": 912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545243285, "dur": 879, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545244164, "dur": 897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545245062, "dur": 844, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545245907, "dur": 627, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545246535, "dur": 588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545247123, "dur": 600, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545247723, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545248097, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 26, "ts": 1756369545248385, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1756369545248615, "dur": 462, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 26, "ts": 1756369545249809, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Editor\\PlasticSCM\\UI\\Progress\\OperationProgressData.cs"}}, {"pid": 12345, "tid": 26, "ts": 1756369545249904, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Editor\\PlasticSCM\\UI\\Tree\\TreeViewItemIds.cs"}}, {"pid": 12345, "tid": 26, "ts": 1756369545248293, "dur": 2219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1756369545250513, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545250846, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 26, "ts": 1756369545251129, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 26, "ts": 1756369545251357, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 26, "ts": 1756369545251663, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 26, "ts": 1756369545250655, "dur": 1202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Animancer.FSM.dll (+2 others)"}}, {"pid": 12345, "tid": 26, "ts": 1756369545251857, "dur": 736, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545252615, "dur": 236, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545252851, "dur": 110, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545252961, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545253186, "dur": 74, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545253260, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545253623, "dur": 349, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545253972, "dur": 170, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545254142, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545254470, "dur": 301, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545254771, "dur": 439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369545255228, "dur": 17154564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 26, "ts": 1756369562409792, "dur": 4897765, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545214099, "dur": 22150, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545236271, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545236347, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1756369545236346, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_958D7C8F7C565BB9.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1756369545236503, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 27, "ts": 1756369545236503, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_B0882F34B7D5F84E.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1756369545236567, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545236906, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1756369545236964, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545237058, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 27, "ts": 1756369545237387, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545237569, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545237694, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Obi.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1756369545238043, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545238150, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Animancer.FSM.rsp"}}, {"pid": 12345, "tid": 27, "ts": 1756369545238295, "dur": 490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545238785, "dur": 866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545239651, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545241101, "dur": 177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545241278, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545241673, "dur": 297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545241970, "dur": 277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545242248, "dur": 993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545243241, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545244240, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545244920, "dur": 824, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545245744, "dur": 709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545246453, "dur": 579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545247032, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545247707, "dur": 666, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545248374, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/CY.LWGUI.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1756369545248754, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/spinemg-unity.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1756369545248872, "dur": 374, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545249249, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/MeshBakerEditor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1756369545249616, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 27, "ts": 1756369545249903, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Plugins\\UnityChannel\\ChannelPurchase.dll"}}, {"pid": 12345, "tid": 27, "ts": 1756369545250518, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 27, "ts": 1756369545249823, "dur": 1308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 27, "ts": 1756369545251131, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545251426, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545251660, "dur": 205, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545251865, "dur": 457, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545252345, "dur": 259, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545252605, "dur": 252, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545252857, "dur": 103, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545252960, "dur": 225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545253185, "dur": 86, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545253271, "dur": 346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545253617, "dur": 372, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545253989, "dur": 175, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545254165, "dur": 275, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545254469, "dur": 309, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369545254778, "dur": 17154971, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 27, "ts": 1756369562409752, "dur": 533638, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 27, "ts": 1756369562409751, "dur": 533641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 27, "ts": 1756369562943407, "dur": 2729, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 27, "ts": 1756369562946142, "dur": 4361437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545213729, "dur": 22195, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545235943, "dur": 321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 28, "ts": 1756369545235927, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_F085666F1110AAE3.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1756369545236265, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545236618, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545236910, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1756369545237403, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545237557, "dur": 97, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/LeTai.TrueShadow.rsp2"}}, {"pid": 12345, "tid": 28, "ts": 1756369545237655, "dur": 372, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ShapesRuntime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1756369545238122, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/ShapesSamples.rsp"}}, {"pid": 12345, "tid": 28, "ts": 1756369545238329, "dur": 747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545239076, "dur": 431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545239507, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545239796, "dur": 711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545240517, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Assets\\TinyGame\\Lib\\lib_Shapes\\Scripts\\Runtime\\Utils\\ShapesAssets.cs"}}, {"pid": 12345, "tid": 28, "ts": 1756369545240507, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545241267, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545242276, "dur": 397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545243193, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.7\\Scripts\\Editor\\TMPro_FontPlugin.cs"}}, {"pid": 12345, "tid": 28, "ts": 1756369545242673, "dur": 2213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545244886, "dur": 735, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545245621, "dur": 759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545246381, "dur": 628, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545247009, "dur": 699, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545247708, "dur": 389, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545248098, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 28, "ts": 1756369545248276, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545248488, "dur": 949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VSCode.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1756369545249438, "dur": 285, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545249809, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Assets\\Parse\\Plugins\\dotNet45\\Unity.Tasks.dll"}}, {"pid": 12345, "tid": 28, "ts": 1756369545250061, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "D:\\Program\\Unity\\Editor\\2022.3.62f1c1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 28, "ts": 1756369545249758, "dur": 1014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/MeshBakerCore.Examples.dll (+2 others)"}}, {"pid": 12345, "tid": 28, "ts": 1756369545250773, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545251499, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\AppleAuth.Editor.dll"}}, {"pid": 12345, "tid": 28, "ts": 1756369545251499, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/AppleAuth.Editor.dll"}}, {"pid": 12345, "tid": 28, "ts": 1756369545251700, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545251856, "dur": 234, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545252093, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545252155, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545252592, "dur": 255, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545252847, "dur": 115, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545252962, "dur": 210, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545253172, "dur": 93, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545253265, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545253615, "dur": 350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545253965, "dur": 171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545254155, "dur": 306, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545254461, "dur": 322, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369545254783, "dur": 17155007, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 28, "ts": 1756369562409790, "dur": 4897870, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1756369567311760, "dur": 2445, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21572, "tid": 429, "ts": 1756369567324070, "dur": 1388, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21572, "tid": 429, "ts": 1756369567325493, "dur": 1523, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21572, "tid": 429, "ts": 1756369567321771, "dur": 5807, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}